import {
  En<PERSON>ty,
  Column,
  PrimaryGenerated<PERSON><PERSON>umn,
  CreateDate<PERSON>olumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { OrderLineItem } from './order-line-item.entity';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';
import { User } from '../../auth/entities/user.entity';
import { TikTokPackage } from './tiktok-package.entity';

export enum OrderStatus {
  UNPAID = 'UNPAID',
  ON_HOLD = 'ON_HOLD',
  AWAITING_SHIPMENT = 'AWAITING_SHIPMENT',
  PARTIALLY_SHIPPING = 'PARTIALLY_SHIPPING',
  AWAITING_COLLECTION = 'AWAITING_COLLECTION',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export interface PaymentInfo {
  totalAmount?: string;
  subTotal?: string;
  currency?: string;
  originalTotalProductPrice?: string;
  tax?: string;
  shippingFee?: string;
  platformDiscount?: string;
  sellerDiscount?: string;
  shippingFeeTax?: string;
  productTax?: string;
  retailDeliveryFee?: string;
  buyerServiceFee?: string;
  handlingFee?: string;
  itemInsuranceFee?: string;
  shippingInsuranceFee?: string;
  smallOrderFee?: string;
  originalShippingFee?: string;
  shippingFeePlatformDiscount?: string;
  shippingFeeSellerDiscount?: string;
  shippingFeeCofundedDiscount?: string;
}

export interface RecipientAddress {
  name?: string;
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  addressDetail?: string;
  addressLine1?: string;
  addressLine2?: string;
  addressLine3?: string;
  addressLine4?: string;
  postalCode?: string;
  regionCode?: string;
  fullAddress?: string;
  districtInfo?: any[];
  deliveryPreferences?: any;
  firstNameLocalScript?: string;
  lastNameLocalScript?: string;
}

@Entity('orders')
export class Order {
  @ApiProperty({ description: 'Order ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Order ID of TikTok Shop',
    example: '7891234567890123456',
  })
  @Column({ nullable: true, unique: true })
  idTT: string;

  @ApiProperty({
    description: 'Order status in TikTok Shop',
    enum: OrderStatus,
    example: OrderStatus.AWAITING_SHIPMENT,
  })
  @Column({
    type: 'enum',
    enum: OrderStatus,
    nullable: true,
  })
  status: OrderStatus;

  @ApiProperty({
    description: 'Order type from TikTok Shop',
    example: 'PRE_ORDER',
  })
  @Column({ nullable: true })
  orderType: string;

  @ApiProperty({
    description: 'Create time from TikTok Shop',
    example: 1640995200,
  })
  @Column({ nullable: true })
  createTimeTT: number;

  @ApiProperty({
    description: 'Update time from TikTok Shop',
    example: 1640995200,
  })
  @Column({ nullable: true })
  updateTimeTT: number;

  @ApiProperty({
    description: 'Paid time from TikTok Shop',
    example: 1640995200,
  })
  @Column({ nullable: true })
  paidTime: number;

  @ApiProperty({
    description: 'Anonymized buyer email',
    example: 'buyer****@example.com',
  })
  @Column({ nullable: true })
  buyerEmail: string;

  @ApiProperty({
    description: 'Note from the buyer',
    example: 'Please deliver after 5 PM',
  })
  @Column('text', { nullable: true })
  buyerMessage: string;

  @ApiProperty({
    description: 'TikTok buyer user ID',
    example: '7123456789',
  })
  @Column({ nullable: true })
  userIdTT: string;

  @ApiProperty({
    description: 'Payment information from TikTok Shop',
    example: {
      totalAmount: '29.99',
      currency: 'USD',
      tax: '2.40',
      shippingFee: '5.99',
    },
  })
  @Column('jsonb', { nullable: true })
  payment: PaymentInfo;

  @ApiProperty({
    description: 'Payment method name',
    example: 'Credit Card',
  })
  @Column({ nullable: true })
  paymentMethodName: string;

  @ApiProperty({
    description: 'Recipient address information',
    example: {
      name: 'John Doe',
      addressLine1: '123 Main St',
      postalCode: '12345',
      regionCode: 'US',
    },
  })
  @Column('jsonb', { nullable: true })
  recipientAddress: RecipientAddress;

  @ApiProperty({
    description: 'Fulfillment type',
    example: 'FULFILLMENT_BY_SELLER',
  })
  @Column({ nullable: true })
  fulfillmentType: string;

  @ApiProperty({
    description: 'Shipping provider name',
    example: 'UPS',
  })
  @Column({ nullable: true })
  shippingProvider: string;

  @ApiProperty({
    description: 'Shipping provider ID',
    example: '12345',
  })
  @Column({ nullable: true })
  shippingProviderId: string;

  @ApiProperty({
    description: 'Shipping type',
    example: 'TIKTOK',
  })
  @Column({ nullable: true })
  shippingType: string;

  @ApiProperty({
    description: 'Tracking number',
    example: '1Z999AA1234567890',
  })
  @Column({ nullable: true })
  trackingNumber: string;

  @ApiProperty({
    description: 'Delivery option ID',
    example: 'standard_shipping',
  })
  @Column({ nullable: true })
  deliveryOptionId: string;

  @ApiProperty({
    description: 'Delivery option name',
    example: 'Standard Shipping',
  })
  @Column({ nullable: true })
  deliveryOptionName: string;

  @ApiProperty({
    description: 'Delivery type (HOME_DELIVERY or COLLECTION_POINT)',
    example: 'HOME_DELIVERY',
  })
  @Column({ nullable: true })
  deliveryType: string;

  @ApiProperty({
    description: 'Whether this is a cash on delivery order',
    example: false,
  })
  @Column({ default: false })
  isCod: boolean;

  @ApiProperty({
    description: 'Whether this is an exchange order',
    example: false,
  })
  @Column({ default: false })
  isExchangeOrder: boolean;

  @ApiProperty({
    description: 'Whether this is a replacement order',
    example: false,
  })
  @Column({ default: false })
  isReplacementOrder: boolean;

  @ApiProperty({
    description: 'Whether this is a sample order',
    example: false,
  })
  @Column({ default: false })
  isSampleOrder: boolean;

  @ApiProperty({
    description: 'Packages information from TikTok Shop',
    example: [
      {
        id: '1154528977690660930'
      }
    ],
  })
  @Column('jsonb', { nullable: true })
  packages: { id: string }[];

  @ApiProperty({
    description: 'Complete raw TikTok response for future extensibility',
  })
  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any;

  @ApiProperty({ description: 'Order line items' })
  @OneToMany(() => OrderLineItem, (lineItem) => lineItem.order, {
    cascade: true,
    eager: false,
  })
  lineItems: OrderLineItem[];

  @ApiProperty({ description: 'TikTok packages for this order' })
  @OneToMany(() => TikTokPackage, (tikTokPackage) => tikTokPackage.order, {
    cascade: true,
    eager: false,
  })
  tikTokPackages: TikTokPackage[];

  @ApiProperty({
    description: 'TikTok Shop ID from internal system',
    example: 1,
  })
  @Column()
  tiktokShopId: number;

  @ManyToOne(() => TikTokShop, (ttShop) => ttShop.orders, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'tiktokShopId' })
  tiktokShop: TikTokShop;

  @ApiProperty({
    description: 'The ID of the user who owns this order',
    example: 1,
  })
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
