import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Order } from './order.entity';
import { User } from '../../auth/entities/user.entity';

@Entity('tiktok_packages')
export class TikTokPackage {
  @ApiProperty({ description: 'Package ID internal system', example: 1 })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'Package ID from TikTok Shop',
    example: '1154528977690660930',
  })
  @Column({ nullable: true })
  packageIdTT: string;

  @ApiProperty({
    description: 'Order ID from TikTok Shop',
    example: '7891234567890123456',
  })
  @Column({ nullable: true })
  orderIdTT: string;

  @ApiProperty({
    description: 'Internal order ID reference',
    example: 123,
  })
  @Column()
  orderId: number;

  @ManyToOne(() => Order, (order) => order.packages, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @ApiProperty({
    description: 'Order line item IDs included in this package',
    example: ['7123456789', '7123456790'],
  })
  @Column('jsonb', { nullable: true })
  orderLineItemIds: string[];

  @ApiProperty({
    description: 'Package creation time from TikTok Shop',
    example: 1640995200,
  })
  @Column({ nullable: true })
  createTimeTT: number;

  @ApiProperty({
    description: 'Shipping service information',
    example: {
      id: 'standard_shipping',
      name: 'Standard Shipping',
    },
  })
  @Column('jsonb', { nullable: true })
  shippingServiceInfo: any;

  @ApiProperty({
    description: 'Package dimensions',
    example: {
      length: 10,
      width: 8,
      height: 5,
      unit: 'cm',
    },
  })
  @Column('jsonb', { nullable: true })
  dimension: any;

  @ApiProperty({
    description: 'Package weight',
    example: {
      value: 1.5,
      unit: 'kg',
    },
  })
  @Column('jsonb', { nullable: true })
  weight: any;

  @ApiProperty({
    description: 'Complete raw TikTok response for future extensibility',
  })
  @Column('jsonb', { nullable: true })
  rawTikTokResponse: any;

  @ApiProperty({
    description: 'Complete raw TikTok response from shipping document API call',
  })
  @Column('jsonb', { nullable: true })
  rawTikTokResponseDocument: any;

  @ApiProperty({
    description: 'Shipping document URL from TikTok Shop (valid for 24 hours)',
    example: 'https://example.com/shipping-label.pdf',
  })
  @Column({ nullable: true })
  docUrl: string;

  @ApiProperty({
    description: 'Package tracking number from the shipping carrier',
    example: 'TT123456789',
  })
  @Column({ nullable: true })
  trackingNumber: string;

  @ApiProperty({
    description: 'The ID of the user who owns this package',
    example: 1,
  })
  @Column({ nullable: true })
  userId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'userId' })
  user: User;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
