import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Order, OrderLineItem, TikTokPackage, OrderStatus } from './entities';
import {
  FilterOrderDto,
  OrderQueryDto,
  OrderSyncDto,
  OrderDetailSyncDto,
  SyncResultDto,
  OrderResponseDto,
  CreatePackageDto,
  CreatePackageResponseDto,
} from './dto';
import { TikTokOrderMapper } from './mappers';
import { TikTokClientFactory } from 'src/tiktok-shop/tiktok-client.factory';
import { TikTokShopService } from '../tiktok-shop/tiktok-shop.service';
import { executeInTransaction } from '../common/utils/transaction.util';
import { PaginatedResult } from '../common/interfaces/paginated-result.interface';

@Injectable()
export class OrdersService {
  private readonly logger = new Logger(OrdersService.name);

  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,
    @InjectRepository(TikTokPackage)
    private readonly tikTokPackageRepository: Repository<TikTokPackage>,
    private readonly clientFactory: TikTokClientFactory,
    private readonly dataSource: DataSource,
    private readonly tikTokOrderMapper: TikTokOrderMapper,
    private readonly tikTokShopService: TikTokShopService,
  ) {}

  /**
   * Synchronize orders from TikTok Shop
   * @param syncDto Synchronization parameters
   * @param userId User ID for authorization
   * @returns Sync result summary
   */
  async synchronizeOrders(
    syncDto: OrderSyncDto,
    userId: number,
  ): Promise<SyncResultDto> {
    this.logger.log(
      `Synchronizing orders for TikTok Shop ID: ${syncDto.tiktokShopId} for user: ${userId}`,
    );

    // Find and validate TikTok Shop
    const tiktokShop = await this.tikTokShopService.findTikTokShopById(
      syncDto.tiktokShopId,
    );

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${syncDto.tiktokShopId} not found`,
      );
    }

    // Verify shop belongs to user
    if (tiktokShop.userId !== userId) {
      throw new NotFoundException(
        `You don't have access to TikTok Shop with ID ${syncDto.tiktokShopId}`,
      );
    }

    // Create TikTok API client
    const client = await this.clientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    const syncResult: SyncResultDto = {
      totalProcessed: 0,
      created: 0,
      updated: 0,
      errors: 0,
      syncedAt: new Date().toISOString(),
      errorMessages: [],
      details: [],
    };

    try {
      let pageToken: string | undefined = syncDto.pageToken;
      let totalProcessed = 0;
      let hasMoreOrders = true;
      const maxOrdersToProcess = syncDto.maxPages ? syncDto.maxPages * (syncDto.pageSize || 50) : 1000;

      while (hasMoreOrders && totalProcessed < maxOrdersToProcess) {
        // Prepare API request parameters
        const requestBody: any = {};
        
        // Add filters if provided
        if (syncDto.statusFilter && syncDto.statusFilter.length > 0) {
          requestBody.status = syncDto.statusFilter;
        }
        if (syncDto.createTimeFrom) {
          requestBody.create_time_from = syncDto.createTimeFrom;
        }
        if (syncDto.createTimeTo) {
          requestBody.create_time_to = syncDto.createTimeTo;
        }
        if (syncDto.updateTimeFrom) {
          requestBody.update_time_from = syncDto.updateTimeFrom;
        }
        if (syncDto.updateTimeTo) {
          requestBody.update_time_to = syncDto.updateTimeTo;
        }

        // Call TikTok Shop API to search for orders
        const result = await client.api.OrderV202309Api.OrdersSearchPost(
          syncDto.pageSize || 50,
          tiktokShop.access_token,
          'application/json',
          syncDto.sortOrder || 'DESC',
          pageToken,
          syncDto.sortField || 'create_time',
          tiktokShop.cipher,
          requestBody,
        );

        // Process orders data
        if (result.body?.data?.orders && result.body.data.orders.length > 0) {
          this.logger.log(
            `Processing ${result.body.data.orders.length} orders from TikTok Shop`,
          );

          // Process each order
          for (const orderData of result.body.data.orders) {
            try {
              const orderResult = await this.processOrderData(
                orderData,
                syncDto.tiktokShopId,
                userId,
              );

              syncResult.totalProcessed++;
              if (orderResult.isNew) {
                syncResult.created++;
              } else {
                syncResult.updated++;
              }

              syncResult.details!.push({
                orderId: orderData.id || 'unknown',
                status: orderResult.isNew ? 'created' : 'updated',
                lineItemsCount: orderData.lineItems?.length || 0,
                internalOrderId: orderResult.order.id,
              });

              totalProcessed++;
            } catch (orderError) {
              this.logger.error(
                `Error processing order ${orderData.id}: ${orderError.message}`,
              );
              syncResult.errors++;
              syncResult.errorMessages!.push(
                `Order ${orderData.id}: ${orderError.message}`,
              );
              syncResult.details!.push({
                orderId: orderData.id || 'unknown',
                status: 'error',
                errorMessage: orderError.message,
              });
            }
          }

          // Check for next page
          const nextPageToken = 
            result.body.data.nextPageToken || 
            (result.body.data as any).next_page_token;
          
          if (nextPageToken) {
            pageToken = nextPageToken;
            syncResult.nextPageToken = nextPageToken;
          } else {
            hasMoreOrders = false;
          }
        } else {
          this.logger.log('No orders found from TikTok Shop');
          hasMoreOrders = false;
        }
      }

      this.logger.log(
        `Successfully processed ${syncResult.totalProcessed} orders from TikTok Shop`,
      );
    } catch (error) {
      this.logger.error(
        'Error synchronizing orders from TikTok Shop:',
        error,
      );
      throw new Error(`Failed to synchronize orders: ${error.message}`);
    }

    return syncResult;
  }

  /**
   * Synchronize detailed order information for specific orders
   * @param syncDto Detail synchronization parameters
   * @param userId User ID for authorization
   * @returns Sync result summary
   */
  async synchronizeOrderDetails(
    syncDto: OrderDetailSyncDto,
    userId: number,
  ): Promise<SyncResultDto> {
    this.logger.log(
      `Synchronizing order details for ${syncDto.orderIds.length} orders from TikTok Shop ID: ${syncDto.tiktokShopId}`,
    );

    // Find and validate TikTok Shop
    const tiktokShop = await this.tikTokShopService.findTikTokShopById(
      syncDto.tiktokShopId,
    );

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${syncDto.tiktokShopId} not found`,
      );
    }

    // Verify shop belongs to user
    if (tiktokShop.userId !== userId) {
      throw new NotFoundException(
        `You don't have access to TikTok Shop with ID ${syncDto.tiktokShopId}`,
      );
    }

    // Validate order IDs limit (TikTok API allows max 50)
    if (syncDto.orderIds.length > 50) {
      throw new Error('Maximum 50 order IDs allowed per request');
    }

    // Create TikTok API client
    const client = await this.clientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    const syncResult: SyncResultDto = {
      totalProcessed: 0,
      created: 0,
      updated: 0,
      errors: 0,
      syncedAt: new Date().toISOString(),
      errorMessages: [],
      details: [],
    };

    try {
      // Call TikTok Shop API to get order details
      const result = await client.api.OrderV202309Api.OrdersGet(
        syncDto.orderIds,
        tiktokShop.access_token,
        'application/json',
        tiktokShop.cipher,
      );

      // Process order details
      if (result.body?.data?.orders && result.body.data.orders.length > 0) {
        this.logger.log(
          `Processing ${result.body.data.orders.length} order details from TikTok Shop`,
        );

        for (const orderData of result.body.data.orders) {
          try {
            const orderResult = await this.processOrderData(
              orderData,
              syncDto.tiktokShopId,
              userId,
            );

            syncResult.totalProcessed++;
            if (orderResult.isNew) {
              syncResult.created++;
            } else {
              syncResult.updated++;
            }

            syncResult.details!.push({
              orderId: orderData.id || 'unknown',
              status: orderResult.isNew ? 'created' : 'updated',
              lineItemsCount: orderData.lineItems?.length || 0,
              internalOrderId: orderResult.order.id,
            });
          } catch (orderError) {
            this.logger.error(
              `Error processing order details ${orderData.id}: ${orderError.message}`,
            );
            syncResult.errors++;
            syncResult.errorMessages!.push(
              `Order ${orderData.id}: ${orderError.message}`,
            );
            syncResult.details!.push({
              orderId: orderData.id || 'unknown',
              status: 'error',
              errorMessage: orderError.message,
            });
          }
        }
      } else {
        this.logger.log('No order details found from TikTok Shop');
      }

      this.logger.log(
        `Successfully processed ${syncResult.totalProcessed} order details from TikTok Shop`,
      );
    } catch (error) {
      this.logger.error(
        'Error synchronizing order details from TikTok Shop:',
        error,
      );
      throw new Error(`Failed to synchronize order details: ${error.message}`);
    }

    return syncResult;
  }

  /**
   * Process individual order data from TikTok API
   * @param orderData Raw order data from TikTok API
   * @param tiktokShopId TikTok Shop ID
   * @param userId User ID
   * @returns Processing result with order and isNew flag
   */
  private async processOrderData(
    orderData: any,
    tiktokShopId: number,
    userId: number,
  ): Promise<{ order: Order; isNew: boolean }> {
    return executeInTransaction(this.dataSource, async (manager) => {
      // Check if order already exists
      const existingOrder = await manager.findOne(Order, {
        where: { idTT: orderData.id },
        relations: ['lineItems'],
      });

      if (existingOrder) {
        // Update existing order
        const updateDto = this.tikTokOrderMapper.mapToUpdateOrderDto(orderData);

        // Merge update data with existing order
        Object.assign(existingOrder, updateDto);

        // Process line items
        if (orderData.lineItems && Array.isArray(orderData.lineItems)) {
          await this.processLineItemsForExistingOrder(
            existingOrder,
            orderData.lineItems,
            manager,
          );
        }

        const savedOrder = await manager.save(existingOrder);
        return { order: savedOrder, isNew: false };
      } else {
        // Create new order
        const createDto = this.tikTokOrderMapper.mapToCreateOrderDto(
          orderData,
          tiktokShopId,
          userId,
        );

        const newOrder = manager.create(Order, createDto);

        // Save order first to get ID
        const savedOrder = await manager.save(newOrder);

        /*
        // Process line items if available
        if (createDto.lineItems && createDto.lineItems.length > 0) {
          const lineItems = createDto.lineItems.map((lineItemDto) => {
            this.logger.debug('Creating line item:', lineItemDto);
            const lineItem = manager.create(OrderLineItem, lineItemDto);
            lineItem.orderId = savedOrder.id;
            return lineItem;
          });

          await manager.save(lineItems);
          savedOrder.lineItems = lineItems;
        }
        */
       
        return { order: savedOrder, isNew: true };
      }
    });
  }

  /**
   * Process line items for an existing order
   * @param existingOrder Existing order entity
   * @param lineItemsData Line items data from TikTok API
   * @param manager Entity manager for transaction
   */
  private async processLineItemsForExistingOrder(
    existingOrder: Order,
    lineItemsData: any[],
    manager: any,
  ): Promise<void> {
    // Create a map of existing line items by TikTok ID
    const existingLineItemsMap = new Map<string, OrderLineItem>();
    if (existingOrder.lineItems) {
      existingOrder.lineItems.forEach((lineItem) => {
        if (lineItem.idTT) {
          existingLineItemsMap.set(lineItem.idTT, lineItem);
        }
      });
    }

    // Track processed line item IDs
    const processedLineItemIds = new Set<string>();

    // Process each line item from API
    for (const lineItemData of lineItemsData) {
      const lineItemDto = this.tikTokOrderMapper.mapToCreateOrderLineItemDto(lineItemData);

      if (lineItemDto.idTT) {
        processedLineItemIds.add(lineItemDto.idTT);

        const existingLineItem = existingLineItemsMap.get(lineItemDto.idTT);

        if (existingLineItem) {
          // Update existing line item
          Object.assign(existingLineItem, lineItemDto);
          await manager.save(existingLineItem);
        } else {
          // Create new line item
          const newLineItem = manager.create(OrderLineItem, lineItemDto);
          newLineItem.orderId = existingOrder.id;
          await manager.save(newLineItem);
        }
      }
    }

    // Remove line items that are no longer in the API response
    for (const [idTT, lineItem] of existingLineItemsMap) {
      if (!processedLineItemIds.has(idTT)) {
        await manager.remove(lineItem);
      }
    }
  }

  /**
   * Find all orders with pagination and filters
   * @param queryDto Query parameters
   * @param userId User ID for authorization
   * @returns Paginated orders result
   */
  async findAll(
    queryDto: OrderQueryDto,
    userId: number,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    this.logger.log(
      `Fetching orders with query: ${JSON.stringify(queryDto)} for user: ${userId}`,
    );

    const { page = 1, pageSize = 20, sortField = 'create_time', sortOrder = 'DESC' } = queryDto;
    const skip = (page - 1) * pageSize;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.tiktokShop', 'tiktokShop')
      .distinct(true);

    // Add user filter
    queryBuilder.andWhere('order.userId = :userId', { userId });

    // Add TikTok Shop filter if specified
    if (queryDto.tiktokShopId) {
      queryBuilder.andWhere('order.tiktokShopId = :tiktokShopId', {
        tiktokShopId: queryDto.tiktokShopId,
      });
    }

    // Apply filters
    this.applyOrderFilters(queryBuilder, queryDto);

    // Apply search
    if (queryDto.search) {
      queryBuilder.andWhere(
        '(order.idTT ILIKE :search OR order.buyerEmail ILIKE :search OR order.trackingNumber ILIKE :search)',
        { search: `%${queryDto.search}%` },
      );
    }

    // Include relations if requested
    if (queryDto.includeLineItems) {
      queryBuilder.leftJoinAndSelect('order.lineItems', 'lineItems');
    }
    if (queryDto.includeUser) {
      queryBuilder.leftJoinAndSelect('order.user', 'user');
    }

    // Apply sorting
    const sortColumn = this.mapSortField(sortField);
    queryBuilder.orderBy(sortColumn, sortOrder);

    // Apply pagination
    queryBuilder.take(pageSize);
    queryBuilder.skip(skip);

    const [orders, total] = await queryBuilder.getManyAndCount();

    // Transform to response DTOs
    const data = orders.map((order) => this.mapToResponseDto(order));

    return {
      data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: pageSize,
        totalPages: Math.ceil(total / pageSize),
        currentPage: page,
      },
    };
  }

  /**
   * Find a single order by ID
   * @param id Order ID
   * @param userId User ID for authorization
   * @returns Order response DTO
   */
  async findOne(id: number, userId: number): Promise<OrderResponseDto> {
    this.logger.log(`Finding order with ID: ${id} for user: ${userId}`);

    const order = await this.orderRepository.findOne({
      where: { id, userId },
      relations: ['lineItems', 'tiktokShop', 'user'],
    });

    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }

    return this.mapToResponseDto(order);
  }

  /**
   * Apply filters to the query builder
   * @param queryBuilder TypeORM query builder
   * @param filters Filter parameters
   */
  private applyOrderFilters(queryBuilder: any, filters: FilterOrderDto): void {
    if (filters.status && filters.status.length > 0) {
      queryBuilder.andWhere('order.status IN (:...statuses)', {
        statuses: filters.status,
      });
    }

    if (filters.orderType) {
      queryBuilder.andWhere('order.orderType = :orderType', {
        orderType: filters.orderType,
      });
    }

    if (filters.fulfillmentType) {
      queryBuilder.andWhere('order.fulfillmentType = :fulfillmentType', {
        fulfillmentType: filters.fulfillmentType,
      });
    }

    if (filters.createTimeFrom) {
      queryBuilder.andWhere('order.createTimeTT >= :createTimeFrom', {
        createTimeFrom: filters.createTimeFrom,
      });
    }

    if (filters.createTimeTo) {
      queryBuilder.andWhere('order.createTimeTT <= :createTimeTo', {
        createTimeTo: filters.createTimeTo,
      });
    }

    if (filters.updateTimeFrom) {
      queryBuilder.andWhere('order.updateTimeTT >= :updateTimeFrom', {
        updateTimeFrom: filters.updateTimeFrom,
      });
    }

    if (filters.updateTimeTo) {
      queryBuilder.andWhere('order.updateTimeTT <= :updateTimeTo', {
        updateTimeTo: filters.updateTimeTo,
      });
    }

    if (filters.paidTimeFrom) {
      queryBuilder.andWhere('order.paidTime >= :paidTimeFrom', {
        paidTimeFrom: filters.paidTimeFrom,
      });
    }

    if (filters.paidTimeTo) {
      queryBuilder.andWhere('order.paidTime <= :paidTimeTo', {
        paidTimeTo: filters.paidTimeTo,
      });
    }

    if (filters.buyerEmail) {
      queryBuilder.andWhere('order.buyerEmail ILIKE :buyerEmail', {
        buyerEmail: `%${filters.buyerEmail}%`,
      });
    }

    if (filters.userIdTT) {
      queryBuilder.andWhere('order.userIdTT = :userIdTT', {
        userIdTT: filters.userIdTT,
      });
    }

    if (filters.paymentMethodName) {
      queryBuilder.andWhere('order.paymentMethodName = :paymentMethodName', {
        paymentMethodName: filters.paymentMethodName,
      });
    }

    if (filters.shippingProvider) {
      queryBuilder.andWhere('order.shippingProvider = :shippingProvider', {
        shippingProvider: filters.shippingProvider,
      });
    }

    if (filters.trackingNumber) {
      queryBuilder.andWhere('order.trackingNumber = :trackingNumber', {
        trackingNumber: filters.trackingNumber,
      });
    }

    if (filters.isCod !== undefined) {
      queryBuilder.andWhere('order.isCod = :isCod', {
        isCod: filters.isCod,
      });
    }

    if (filters.isExchangeOrder !== undefined) {
      queryBuilder.andWhere('order.isExchangeOrder = :isExchangeOrder', {
        isExchangeOrder: filters.isExchangeOrder,
      });
    }

    if (filters.isReplacementOrder !== undefined) {
      queryBuilder.andWhere('order.isReplacementOrder = :isReplacementOrder', {
        isReplacementOrder: filters.isReplacementOrder,
      });
    }

    if (filters.isSampleOrder !== undefined) {
      queryBuilder.andWhere('order.isSampleOrder = :isSampleOrder', {
        isSampleOrder: filters.isSampleOrder,
      });
    }

    if (filters.currency) {
      queryBuilder.andWhere("order.payment->>'currency' = :currency", {
        currency: filters.currency,
      });
    }

    if (filters.minTotalAmount !== undefined) {
      queryBuilder.andWhere(
        "CAST(order.payment->>'totalAmount' AS DECIMAL) >= :minTotalAmount",
        { minTotalAmount: filters.minTotalAmount },
      );
    }

    if (filters.maxTotalAmount !== undefined) {
      queryBuilder.andWhere(
        "CAST(order.payment->>'totalAmount' AS DECIMAL) <= :maxTotalAmount",
        { maxTotalAmount: filters.maxTotalAmount },
      );
    }
  }

  /**
   * Map sort field to database column
   * @param sortField Sort field from DTO
   * @returns Database column name
   */
  private mapSortField(sortField: string): string {
    const sortFieldMap: Record<string, string> = {
      'create_time': 'order.createTimeTT',
      'update_time': 'order.updateTimeTT',
      'paid_time': 'order.paidTime',
      'total_amount': "CAST(order.payment->>'totalAmount' AS DECIMAL)",
      'status': 'order.status',
    };

    return sortFieldMap[sortField] || 'order.createTimeTT';
  }

  /**
   * Map Order entity to OrderResponseDto
   * @param order Order entity
   * @returns OrderResponseDto
   */
  private mapToResponseDto(order: Order): OrderResponseDto {
    const responseDto: OrderResponseDto = {
      id: order.id,
      idTT: order.idTT,
      status: order.status,
      orderType: order.orderType,
      createTimeTT: order.createTimeTT,
      updateTimeTT: order.updateTimeTT,
      paidTime: order.paidTime,
      buyerEmail: order.buyerEmail,
      buyerMessage: order.buyerMessage,
      userIdTT: order.userIdTT,
      payment: order.payment,
      paymentMethodName: order.paymentMethodName,
      recipientAddress: order.recipientAddress,
      fulfillmentType: order.fulfillmentType,
      shippingProvider: order.shippingProvider,
      shippingType: order.shippingType,
      trackingNumber: order.trackingNumber,
      deliveryOptionName: order.deliveryOptionName,
      deliveryType: order.deliveryType,
      isCod: order.isCod,
      isExchangeOrder: order.isExchangeOrder,
      isReplacementOrder: order.isReplacementOrder,
      isSampleOrder: order.isSampleOrder,
      packages: order.packages,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    };

    // Include line items if available
    if (order.lineItems) {
      responseDto.lineItems = order.lineItems.map((lineItem) => ({
        id: lineItem.id,
        idTT: lineItem.idTT,
        productIdTT: lineItem.productIdTT,
        skuIdTT: lineItem.skuIdTT,
        sellerSku: lineItem.sellerSku,
        productName: lineItem.productName,
        skuName: lineItem.skuName,
        skuImage: lineItem.skuImage,
        originalPrice: lineItem.originalPrice,
        salePrice: lineItem.salePrice,
        currency: lineItem.currency,
        platformDiscount: lineItem.platformDiscount,
        sellerDiscount: lineItem.sellerDiscount,
        packageId: lineItem.packageId,
        packageStatus: lineItem.packageStatus,
        displayStatus: lineItem.displayStatus,
        isDangerousGood: lineItem.isDangerousGood,
        isGift: lineItem.isGift,
        quantity: lineItem.quantity,
        retailDeliveryFee: lineItem.retailDeliveryFee,
        buyerServiceFee: lineItem.buyerServiceFee,
        smallOrderFee: lineItem.smallOrderFee,
        trackingNumber: lineItem.trackingNumber,
        createdAt: lineItem.createdAt,
        updatedAt: lineItem.updatedAt,
      }));
    }

    // Include TikTok Shop if available
    if (order.tiktokShop) {
      responseDto.tiktokShop = {
        id: order.tiktokShop.id,
        idTT: order.tiktokShop.idTT,
        name: order.tiktokShop.name,
        region: order.tiktokShop.region,
        friendly_name: order.tiktokShop.friendly_name,
        code: order.tiktokShop.code,
      };
    }

    // Include user if available
    if (order.user) {
      responseDto.user = {
        id: order.user.id,
        email: order.user.email,
        name: order.user.name,
      };
    }

    return responseDto;
  }

  /**
   * Create a package for an order using TikTok Shop API
   * @param createPackageDto Package creation parameters
   * @param userId User ID for authorization
   * @returns Created package information
   */
  async createPackage(
    createPackageDto: CreatePackageDto,
    userId: number,
  ): Promise<CreatePackageResponseDto> {
    this.logger.log(
      `Creating package for order ${createPackageDto.orderId} from TikTok Shop ID: ${createPackageDto.tiktokShopId}`,
    );

    // Find and validate TikTok Shop
    const tiktokShop = await this.tikTokShopService.findTikTokShopById(
      createPackageDto.tiktokShopId,
    );

    if (!tiktokShop) {
      throw new NotFoundException(
        `TikTok Shop with ID ${createPackageDto.tiktokShopId} not found`,
      );
    }

    // Verify shop belongs to user
    if (tiktokShop.userId !== userId) {
      throw new NotFoundException(
        `You don't have access to TikTok Shop with ID ${createPackageDto.tiktokShopId}`,
      );
    }

    // Find and validate the order
    const order = await this.orderRepository.findOne({
      where: {
        id: createPackageDto.orderId,
        userId: userId,
      },
    });

    if (!order) {
      throw new NotFoundException(
        `Order with ID ${createPackageDto.orderId} not found or access denied`,
      );
    }

    // Verify the order belongs to the specified TikTok Shop
    if (order.tiktokShopId !== createPackageDto.tiktokShopId) {
      throw new NotFoundException(
        `Order ${createPackageDto.orderId} does not belong to TikTok Shop ${createPackageDto.tiktokShopId}`,
      );
    }

    // Verify order status is AWAITING_SHIPMENT
    if (order.status !== OrderStatus.AWAITING_SHIPMENT) {
      throw new BadRequestException(
        `Order ${createPackageDto.orderId} must be in AWAITING_SHIPMENT status to create package. Current status: ${order.status}`,
      );
    }

    // Verify shipping type is TIKTOK
    if (order.shippingType !== 'TIKTOK') {
      throw new BadRequestException(
        `Order ${createPackageDto.orderId} must have TIKTOK shipping type to create package. Current shipping type: ${order.shippingType}`,
      );
    }

    // Verify fulfillment type is FULFILLMENT_BY_SELLER
    if (order.fulfillmentType !== 'FULFILLMENT_BY_SELLER') {
      throw new BadRequestException(
        `Order ${createPackageDto.orderId} must have FULFILLMENT_BY_SELLER fulfillment type to create package. Current fulfillment type: ${order.fulfillmentType}`,
      );
    }

    // Create TikTok API client
    const client = await this.clientFactory.createClientByAppKey(
      tiktokShop.app_key,
    );

    try {
      // Prepare the request body for TikTok Shop API
      const requestBody: any = {
        orderId: createPackageDto.orderIdTT,
      };

      // Add orderLineItemIds only if provided
      if (createPackageDto.orderLineItemIds && createPackageDto.orderLineItemIds.length > 0) {
        requestBody.orderLineItemIds = createPackageDto.orderLineItemIds;
      }

      // Add optional fields if provided
      if (createPackageDto.shippingServiceId) {
        requestBody.shippingServiceId = createPackageDto.shippingServiceId;
      }

      if (createPackageDto.dimension) {
        requestBody.dimension = {
          ...createPackageDto.dimension,
          // Convert numeric values to strings as required by TikTok Shop SDK
          length: createPackageDto.dimension.length?.toString(),
          width: createPackageDto.dimension.width?.toString(),
          height: createPackageDto.dimension.height?.toString(),
        };
      }

      if (createPackageDto.weight) {
        requestBody.weight = {
          ...createPackageDto.weight,
          // Convert numeric value to string as required by TikTok Shop SDK
          value: createPackageDto.weight.value?.toString(),
        };
      }

      this.logger.log(
        `Calling TikTok Shop API to create package with request: ${JSON.stringify(requestBody)}`,
      );

      // Call TikTok Shop API to create package
      const result = await client.api.FulfillmentV202309Api.PackagesPost(
        tiktokShop.access_token,
        'application/json',
        tiktokShop.cipher,
        requestBody,
      );

      this.logger.log(
        `TikTok Shop API response: ${JSON.stringify(result.body)}`,
      );

      if (!result.body?.data) {
        throw new Error('Invalid response from TikTok Shop API');
      }

      const packageData = result.body.data;

      // Check if package already exists (by packageIdTT or orderId + orderIdTT combination)
      let existingPackage = await this.tikTokPackageRepository.findOne({
        where: [
          { packageIdTT: packageData.packageId },
          {
            orderId: createPackageDto.orderId,
            orderIdTT: createPackageDto.orderIdTT,
          },
        ],
      });

      let savedPackage: TikTokPackage;

      if (existingPackage) {
        // Update existing package
        this.logger.log(
          `Updating existing package with ID: ${existingPackage.id} (TikTok Package ID: ${existingPackage.packageIdTT})`,
        );

        // Update the existing package with new data
        if (packageData.packageId) {
          existingPackage.packageIdTT = packageData.packageId;
        }
        existingPackage.orderIdTT = createPackageDto.orderIdTT;
        existingPackage.orderLineItemIds = packageData.orderLineItemIds || createPackageDto.orderLineItemIds || [];
        if (packageData.createTime !== undefined) {
          existingPackage.createTimeTT = packageData.createTime;
        }
        existingPackage.shippingServiceInfo = packageData.shippingServiceInfo;
        existingPackage.dimension = packageData.dimension;
        existingPackage.weight = packageData.weight;
        existingPackage.rawTikTokResponse = result.body;
        // Note: userId and orderId should not change for existing packages

        savedPackage = await this.tikTokPackageRepository.save(existingPackage);
        this.logger.log(
          `Successfully updated package with ID: ${savedPackage.id} (TikTok Package ID: ${savedPackage.packageIdTT})`,
        );
      } else {
        // Create new package
        const tikTokPackage = this.tikTokPackageRepository.create({
          packageIdTT: packageData.packageId,
          orderIdTT: createPackageDto.orderIdTT,
          orderId: createPackageDto.orderId,
          orderLineItemIds: packageData.orderLineItemIds || createPackageDto.orderLineItemIds || [],
          createTimeTT: packageData.createTime,
          shippingServiceInfo: packageData.shippingServiceInfo,
          dimension: packageData.dimension,
          weight: packageData.weight,
          rawTikTokResponse: result.body,
          userId: userId,
        });

        savedPackage = await this.tikTokPackageRepository.save(tikTokPackage);
        this.logger.log(
          `Successfully created package with ID: ${savedPackage.id} (TikTok Package ID: ${savedPackage.packageIdTT})`,
        );
      }

      // Return the response DTO
      return {
        id: savedPackage.id,
        packageIdTT: savedPackage.packageIdTT,
        orderIdTT: savedPackage.orderIdTT,
        orderId: savedPackage.orderId,
        orderLineItemIds: savedPackage.orderLineItemIds,
        createTimeTT: savedPackage.createTimeTT,
        shippingServiceInfo: savedPackage.shippingServiceInfo,
        dimension: savedPackage.dimension,
        weight: savedPackage.weight,
        createdAt: savedPackage.createdAt,
        updatedAt: savedPackage.updatedAt,
        isNew: !existingPackage, // true if it was a new creation, false if it was an update
      };
    } catch (error) {
      this.logger.error(
        'Error creating package via TikTok Shop API:',
        error,
      );
      throw new Error(`Failed to create package: ${error.message}`);
    }
  }
}
