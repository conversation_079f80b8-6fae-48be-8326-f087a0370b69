import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsArray, IsE<PERSON>, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus } from '../entities/order.entity';

export enum TikTokOrderSortField {
  CREATE_TIME = 'create_time',
  UPDATE_TIME = 'update_time',
}

export enum TikTokSortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class OrderSyncDto {
  @ApiProperty({
    description: 'TikTok Shop ID to synchronize orders from',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId: number;

  @ApiPropertyOptional({
    description: 'Number of orders to fetch per page (max 50)',
    example: 50,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(50)
  pageSize?: number = 50;

  @ApiPropertyOptional({
    description: 'Sort order for fetching orders',
    enum: TikTokSortOrder,
    example: TikTokSortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(TikTokSortOrder)
  sortOrder?: TikTokSortOrder = TikTokSortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Page token for pagination (from TikTok API)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsOptional()
  @IsString()
  pageToken?: string;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: TikTokOrderSortField,
    example: TikTokOrderSortField.CREATE_TIME,
  })
  @IsOptional()
  @IsEnum(TikTokOrderSortField)
  sortField?: TikTokOrderSortField = TikTokOrderSortField.CREATE_TIME;

  @ApiPropertyOptional({
    description: 'Filter by order status',
    enum: OrderStatus,
    isArray: true,
    example: [OrderStatus.AWAITING_SHIPMENT, OrderStatus.IN_TRANSIT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OrderStatus, { each: true })
  statusFilter?: OrderStatus[];

  @ApiPropertyOptional({
    description: 'Start time for order creation filter (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'End time for order creation filter (Unix timestamp)',
    example: 1672531199,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Start time for order update filter (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'End time for order update filter (Unix timestamp)',
    example: 1672531199,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of pages to sync (safety limit)',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  maxPages?: number = 10;

  @ApiPropertyOptional({
    description: 'Force full sync (ignore last sync timestamp)',
    example: false,
  })
  @IsOptional()
  forceFullSync?: boolean = false;
}

export class OrderSyncMultiShopDto {
  @ApiProperty({
    description: 'Array of TikTok Shop IDs to synchronize orders from',
    example: [1, 2, 3],
    isArray: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  tiktokShopIds: number[];

  @ApiPropertyOptional({
    description: 'Number of orders to fetch per page (max 50)',
    example: 50,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(50)
  pageSize?: number = 50;

  @ApiPropertyOptional({
    description: 'Sort order for fetching orders',
    enum: TikTokSortOrder,
    example: TikTokSortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(TikTokSortOrder)
  sortOrder?: TikTokSortOrder = TikTokSortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: TikTokOrderSortField,
    example: TikTokOrderSortField.CREATE_TIME,
  })
  @IsOptional()
  @IsEnum(TikTokOrderSortField)
  sortField?: TikTokOrderSortField = TikTokOrderSortField.CREATE_TIME;

  @ApiPropertyOptional({
    description: 'Filter by order status',
    enum: OrderStatus,
    isArray: true,
    example: [OrderStatus.AWAITING_SHIPMENT, OrderStatus.IN_TRANSIT],
  })
  @IsOptional()
  @IsArray()
  @IsEnum(OrderStatus, { each: true })
  statusFilter?: OrderStatus[];

  @ApiPropertyOptional({
    description: 'Start time for order creation filter (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'End time for order creation filter (Unix timestamp)',
    example: 1672531200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Start time for order update filter (Unix timestamp)',
    example: 1640995200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeFrom?: number;

  @ApiPropertyOptional({
    description: 'End time for order update filter (Unix timestamp)',
    example: 1672531200,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  updateTimeTo?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of pages to fetch per shop (1-100)',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  maxPages?: number = 10;

  @ApiPropertyOptional({
    description: 'Force full sync (ignore last sync timestamp)',
    example: false,
  })
  @IsOptional()
  forceFullSync?: boolean = false;
}

export class OrderDetailSyncDto {
  @ApiProperty({
    description: 'TikTok Shop ID to synchronize order details from',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId: number;

  @ApiProperty({
    description: 'Array of TikTok order IDs to fetch details for (max 50)',
    example: ['7123456789', '7123456790', '7123456791'],
    isArray: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsString({ each: true })
  @Type(() => String)
  orderIds: string[];
}

export class SyncResultDto {
  @ApiProperty({
    description: 'Total number of orders processed',
    example: 150,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Number of orders created',
    example: 45,
  })
  created: number;

  @ApiProperty({
    description: 'Number of orders updated',
    example: 105,
  })
  updated: number;

  @ApiProperty({
    description: 'Number of errors encountered',
    example: 0,
  })
  errors: number;

  @ApiProperty({
    description: 'Timestamp when sync was completed',
    example: '2024-01-15T10:30:00Z',
  })
  syncedAt: string;

  @ApiPropertyOptional({
    description: 'Next page token for continued pagination',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  nextPageToken?: string;

  @ApiPropertyOptional({
    description: 'Detailed error messages',
    example: ['Order 7123456789: Invalid status', 'Order 7123456790: Missing required field'],
  })
  errorMessages?: string[];

  @ApiPropertyOptional({
    description: 'Processing details for each order',
    example: [
      {
        orderId: '7123456789',
        status: 'created',
        lineItemsCount: 2,
      },
      {
        orderId: '7123456790',
        status: 'updated',
        lineItemsCount: 1,
      },
    ],
  })
  details?: OrderSyncDetailDto[];
}

export class OrderSyncDetailDto {
  @ApiProperty({
    description: 'TikTok order ID',
    example: '7123456789',
  })
  orderId: string;

  @ApiProperty({
    description: 'Processing status',
    example: 'created',
    enum: ['created', 'updated', 'skipped', 'error'],
  })
  status: 'created' | 'updated' | 'skipped' | 'error';

  @ApiPropertyOptional({
    description: 'Number of line items processed',
    example: 2,
  })
  lineItemsCount?: number;

  @ApiPropertyOptional({
    description: 'Error message if processing failed',
    example: 'Invalid order status',
  })
  errorMessage?: string;

  @ApiPropertyOptional({
    description: 'Internal order ID after processing',
    example: 123,
  })
  internalOrderId?: number;
}
