import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class CreatePackageDto {
  @ApiProperty({
    description: 'TikTok Shop ID to create package for',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId: number;

  @ApiProperty({
    description: 'Internal order ID to create package for',
    example: 123,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  orderId: number;

  @ApiProperty({
    description: 'TikTok Shop order ID',
    example: '7891234567890123456',
  })
  @IsNotEmpty()
  @IsString()
  orderIdTT: string;

  @ApiPropertyOptional({
    description: 'List of order line item IDs to include in package (optional)',
    example: ['7123456789', '7123456790'],
    isArray: true,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  orderLineItemIds?: string[];

  @ApiPropertyOptional({
    description: 'Shipping service ID to use for the package',
    example: 'standard_shipping',
  })
  @IsOptional()
  @IsString()
  shippingServiceId?: string;

  @ApiPropertyOptional({
    description: 'Package dimensions',
    example: {
      length: 10,
      width: 8,
      height: 5,
      unit: 'cm',
    },
  })
  @IsOptional()
  dimension?: {
    length?: number;
    width?: number;
    height?: number;
    unit?: string;
  };

  @ApiPropertyOptional({
    description: 'Package weight',
    example: {
      value: 1.5,
      unit: 'kg',
    },
  })
  @IsOptional()
  weight?: {
    value?: number;
    unit?: string;
  };
}

export class CreatePackageResponseDto {
  @ApiProperty({
    description: 'Package ID internal system',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Package ID from TikTok Shop',
    example: '1154528977690660930',
  })
  packageIdTT: string;

  @ApiProperty({
    description: 'Order ID from TikTok Shop',
    example: '7891234567890123456',
  })
  orderIdTT: string;

  @ApiProperty({
    description: 'Internal order ID reference',
    example: 123,
  })
  orderId: number;

  @ApiProperty({
    description: 'Order line item IDs included in this package',
    example: ['7123456789', '7123456790'],
  })
  orderLineItemIds: string[];

  @ApiProperty({
    description: 'Package creation time from TikTok Shop',
    example: 1640995200,
  })
  createTimeTT: number;

  @ApiProperty({
    description: 'Shipping service information',
    example: {
      id: 'standard_shipping',
      name: 'Standard Shipping',
    },
  })
  shippingServiceInfo: any;

  @ApiProperty({
    description: 'Package dimensions',
    example: {
      length: 10,
      width: 8,
      height: 5,
      unit: 'cm',
    },
  })
  dimension: any;

  @ApiProperty({
    description: 'Package weight',
    example: {
      value: 1.5,
      unit: 'kg',
    },
  })
  weight: any;

  @ApiProperty({ description: 'Created Date' })
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  updatedAt: Date;

  @ApiProperty({
    description: 'Whether this was a new package creation or an update of existing package',
    example: true
  })
  isNew?: boolean;
}
