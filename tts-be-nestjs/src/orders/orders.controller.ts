import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  BadRequestException,
} from '@nestjs/common';
import { OrdersService } from './orders.service';
import { OrderQueueService } from '../queues/services/order-queue.service';
import {
  OrderQueryDto,
  OrderSyncDto,
  OrderSyncMultiShopDto,
  OrderDetailSyncDto,
  SyncResultDto,
  OrderResponseDto,
  CreatePackageDto,
  CreatePackageResponseDto,
  ShippingLabelResponseDto,
} from './dto';
import { PaginatedResult } from '../common/interfaces/paginated-result.interface';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { CurrentUser } from '../auth/decorators/current-user.decorator';

@ApiTags('Orders')
@Controller('orders')
export class OrdersController {
  constructor(
    private readonly ordersService: OrdersService,
    private readonly orderQueueService: OrderQueueService,
  ) {}

  @ApiOperation({ 
    summary: 'Synchronize orders from TikTok Shop',
    description: 'Fetch and synchronize order list from TikTok Shop API with pagination and filtering support'
  })
  @ApiResponse({
    status: 200,
    description: 'Order synchronization job queued successfully',
    schema: {
      type: 'object',
      properties: {
        jobId: { type: 'string' },
        tiktokShopId: { type: 'number' },
        shopName: { type: 'string' },
        status: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'TikTok Shop not found or access denied',
  })
  @ApiBody({
    type: OrderSyncDto,
    description: 'Order synchronization parameters',
  })
  @Post('synchronize')
  @HttpCode(HttpStatus.OK)
  async synchronizeOrders(
    @Body() syncDto: OrderSyncDto,
    @CurrentUser('id') userId: number,
  ): Promise<{
    jobId: string | number;
    tiktokShopId: number;
    shopName: string;
    status: string;
  }> {
    // Validate DTO
    const validatedDto = plainToInstance(OrderSyncDto, syncDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    // Queue the job instead of processing directly
    return this.orderQueueService.addOrderSyncJob({
      syncDto: validatedDto,
      userId,
    });
  }

  @ApiOperation({
    summary: 'Synchronize orders from multiple TikTok Shops',
    description: 'Fetch and synchronize order lists from multiple TikTok Shops simultaneously. Creates separate jobs for each shop.'
  })
  @ApiResponse({
    status: 200,
    description: 'Order synchronization jobs queued successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          jobId: { type: 'string' },
          tiktokShopId: { type: 'number' },
          shopName: { type: 'string' },
          status: { type: 'string' },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'One or more TikTok Shops not found or access denied',
  })
  @ApiBody({
    type: OrderSyncMultiShopDto,
    description: 'Multi-shop order synchronization parameters',
  })
  @Post('synchronize-multi-shop')
  @HttpCode(HttpStatus.OK)
  async synchronizeOrdersMultiShop(
    @Body() syncDto: OrderSyncMultiShopDto,
    @CurrentUser('id') userId: number,
  ): Promise<Array<{
    jobId: string | number;
    tiktokShopId: number;
    shopName: string;
    status: string;
  }>> {
    // Validate DTO
    const validatedDto = plainToInstance(OrderSyncMultiShopDto, syncDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    // Queue multiple jobs (one per shop)
    return this.orderQueueService.addOrderSyncMultiShopJobs({
      syncDto: validatedDto,
      userId,
    });
  }

  @ApiOperation({
    summary: 'Get order synchronization job status',
    description: 'Check the status and progress of an order synchronization job'
  })
  @ApiResponse({
    status: 200,
    description: 'Job status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        exists: { type: 'boolean' },
        id: { type: 'string' },
        state: { type: 'string' },
        progress: { type: 'number' },
        data: { type: 'object' },
        returnvalue: { type: 'object' },
        failedReason: { type: 'string' },
        timestamp: { type: 'number' },
        finishedOn: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Job not found',
  })
  @ApiParam({
    name: 'jobId',
    description: 'Job ID to check status for',
    type: 'string',
  })
  @Get('sync-job-status/:jobId')
  async getOrderSyncJobStatus(@Param('jobId') jobId: string) {
    return this.orderQueueService.getJobStatus(jobId);
  }

  @ApiOperation({
    summary: 'Synchronize order details from TikTok Shop',
    description: 'Fetch detailed order information for specific orders (max 50 orders per request)'
  })
  @ApiResponse({
    status: 200,
    description: 'Order details synchronized successfully',
    type: SyncResultDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters or too many order IDs',
  })
  @ApiResponse({
    status: 404,
    description: 'TikTok Shop not found or access denied',
  })
  @ApiBody({
    type: OrderDetailSyncDto,
    description: 'Order detail synchronization parameters',
  })
  @Post('synchronize-details')
  @HttpCode(HttpStatus.OK)
  async synchronizeOrderDetails(
    @Body() syncDto: OrderDetailSyncDto,
    @CurrentUser('id') userId: number,
  ): Promise<SyncResultDto> {
    // Validate DTO
    const validatedDto = plainToInstance(OrderDetailSyncDto, syncDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.synchronizeOrderDetails(validatedDto, userId);
  }

  @ApiOperation({
    summary: 'Get all orders with pagination and filters',
    description: 'Retrieve orders with advanced filtering, pagination, and sorting options. OrderLineItems are included by default in the response.'
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    type: 'PaginatedResult<OrderResponseDto>',
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid query parameters',
  })
  @Get()
  async findAll(
    @Query() query: any,
    @CurrentUser('id') userId: number,
  ): Promise<PaginatedResult<OrderResponseDto>> {
    // Validate query parameters
    const queryDto = plainToInstance(OrderQueryDto, query);
    const errors = validateSync(queryDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.findAll(queryDto, userId);
  }

  @ApiOperation({ 
    summary: 'Get order by ID',
    description: 'Retrieve a specific order by its internal ID with all related data'
  })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    type: OrderResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  @ApiParam({
    name: 'id',
    description: 'Order ID from internal system',
    type: 'number',
    example: 1,
  })
  @Get(':id')
  async findOne(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser('id') userId: number,
  ): Promise<OrderResponseDto> {
    return this.ordersService.findOne(id, userId);
  }

  @ApiOperation({
    summary: 'Create a package for an order',
    description: 'Create a package for an order using TikTok Shop API. This will call the TikTok Shop PackagesPost API and store the package information locally.'
  })
  @ApiResponse({
    status: 201,
    description: 'Package created successfully',
    type: CreatePackageResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'Order or TikTok Shop not found or access denied',
  })
  @ApiBody({
    type: CreatePackageDto,
    description: 'Package creation parameters',
  })
  @Post('create-package')
  @HttpCode(HttpStatus.CREATED)
  async createPackage(
    @Body() createPackageDto: CreatePackageDto,
    @CurrentUser('id') userId: number,
  ): Promise<CreatePackageResponseDto> {
    // Validate DTO
    const validatedDto = plainToInstance(CreatePackageDto, createPackageDto);
    const errors = validateSync(validatedDto);

    if (errors.length > 0) {
      throw new BadRequestException(errors);
    }

    return this.ordersService.createPackage(validatedDto, userId);
  }

  @ApiOperation({
    summary: 'Get shipping label for an order',
    description: 'Retrieve shipping label document (PDF) and tracking information for a specific order'
  })
  @ApiResponse({
    status: 200,
    description: 'Shipping label retrieved successfully',
    type: ShippingLabelResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid order ID or missing package information',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied - user does not own this order',
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found or no package ID available',
  })
  @ApiParam({
    name: 'orderId',
    description: 'Internal order ID to get shipping label for',
    type: 'number',
    example: 123,
  })
  @Get('shipping-label/:orderId')
  @HttpCode(HttpStatus.OK)
  async getShippingLabel(
    @Param('orderId', ParseIntPipe) orderId: number,
    @CurrentUser('id') userId: number,
  ): Promise<ShippingLabelResponseDto> {
    return this.ordersService.getShippingLabel(orderId, userId);
  }
}
