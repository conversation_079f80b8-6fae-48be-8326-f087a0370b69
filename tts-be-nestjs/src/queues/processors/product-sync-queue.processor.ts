import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ProductsService } from '../../products/products.service';
import { ProductSyncDto, ProductSyncResultDto } from '../../products/dto/product-sync.dto';
import { TikTokShop } from '../../tiktok-shop/entities/tiktok-shop.entity';

@Processor('product-sync')
@Injectable()
export class ProductSyncQueueProcessor {
  private readonly logger = new Logger(ProductSyncQueueProcessor.name);

  constructor(
    private readonly productsService: ProductsService,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
  ) {}

  @Process('sync-products')
  async handleProductSync(job: Job): Promise<{
    success: boolean;
    message: string;
    jobId: string | number;
    syncResult: ProductSyncResultDto;
  }> {
    this.logger.log(
      `Processing product synchronization job with ID: ${job.id}`,
    );

    try {
      // Extract job data
      const { syncDto, userId } = job.data;
      
      if (!syncDto || !userId) {
        throw new BadRequestException(
          'Missing required job data: syncDto or userId',
        );
      }

      // Initialize progress
      await job.progress(10);
      this.logger.log('Product synchronization job: 10% - Initialized');

      // Call the product synchronization logic
      const syncResult = await this.performProductSync(syncDto, userId, job);

      // Update progress
      await job.progress(100);
      this.logger.log('Product synchronization job: 100% - Completed');

      return {
        success: true,
        message: 'Product synchronization job completed successfully',
        jobId: job.id,
        syncResult,
      };
    } catch (error) {
      this.logger.error(
        `Product synchronization job ${job.id} failed: ${error.message}`,
        error.stack,
      );

      // Update progress to indicate failure
      await job.progress(0);

      throw error;
    }
  }

  /**
   * Perform the actual product synchronization
   * @param syncDto Product sync parameters
   * @param userId User ID
   * @param job Bull job instance for progress updates
   * @returns Sync result
   */
  private async performProductSync(
    syncDto: ProductSyncDto,
    userId: number,
    job: Job,
  ): Promise<ProductSyncResultDto> {
    this.logger.log(
      `Starting product synchronization for TikTok Shop ID: ${syncDto.tiktokShopId}`,
    );

    // Verify that the TikTokShop exists and belongs to the user
    const tiktokShop = await this.tikTokShopRepository.findOne({
      where: {
        id: syncDto.tiktokShopId,
        userId: userId,
      },
    });

    if (!tiktokShop) {
      throw new BadRequestException(
        `TikTok Shop with ID ${syncDto.tiktokShopId} not found or access denied`,
      );
    }

    // Update progress
    await job.progress(20);
    this.logger.log('Product synchronization job: 20% - TikTok Shop verified');

    // Initialize sync result
    const syncResult: ProductSyncResultDto = {
      totalProcessed: 0,
      created: 0,
      updated: 0,
      errors: 0,
      syncedAt: new Date().toISOString(),
      errorMessages: [],
      details: [],
    };

    try {
      // Call the existing synchronize_tiktok method from ProductsService
      // This method already handles pagination, error handling, and product processing
      const result = await this.productsService.synchronize_tiktok(
        syncDto.tiktokShopId,
        userId,
      );

      // Map the result to our sync result format
      syncResult.created = result.created;
      syncResult.updated = result.updated;
      syncResult.totalProcessed = result.created + result.updated;

      // Update progress based on completion
      await job.progress(90);
      this.logger.log(
        `Product synchronization job: 90% - Processed ${syncResult.totalProcessed} products`,
      );

      this.logger.log(
        `Product synchronization completed for TikTok Shop ID: ${syncDto.tiktokShopId}. ` +
        `Created: ${syncResult.created}, Updated: ${syncResult.updated}`,
      );

      return syncResult;
    } catch (error) {
      this.logger.error(
        `Error during product synchronization for TikTok Shop ID: ${syncDto.tiktokShopId}: ${error.message}`,
        error.stack,
      );

      syncResult.errors = 1;
      syncResult.errorMessages!.push(error.message);

      throw error;
    }
  }
}
