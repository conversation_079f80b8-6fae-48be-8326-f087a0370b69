import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsArray, IsEnum, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

export enum TikTokProductSortField {
  CREATE_TIME = 'create_time',
  UPDATE_TIME = 'update_time',
}

export enum TikTokSortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class ProductSyncDto {
  @ApiProperty({
    description: 'TikTok Shop ID to synchronize products from',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  @Type(() => Number)
  tiktokShopId: number;

  @ApiPropertyOptional({
    description: 'Number of products to fetch per page (max 100)',
    example: 100,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  pageSize?: number = 100;

  @ApiPropertyOptional({
    description: 'Sort order for fetching products',
    enum: TikTokSortOrder,
    example: TikTokSortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(TikTokSortOrder)
  sortOrder?: TikTokSortOrder = TikTokSortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Page token for pagination (from TikTok API)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsOptional()
  @IsString()
  pageToken?: string;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: TikTokProductSortField,
    example: TikTokProductSortField.CREATE_TIME,
  })
  @IsOptional()
  @IsEnum(TikTokProductSortField)
  sortField?: TikTokProductSortField = TikTokProductSortField.CREATE_TIME;

  @ApiPropertyOptional({
    description: 'Maximum number of pages to sync (safety limit)',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  maxPages?: number = 10;

  @ApiPropertyOptional({
    description: 'Force full sync (ignore last sync timestamp)',
    example: false,
  })
  @IsOptional()
  forceFullSync?: boolean = false;
}

export class ProductSyncMultiShopDto {
  @ApiProperty({
    description: 'Array of TikTok Shop IDs to synchronize products from',
    example: [1, 2, 3],
    isArray: true,
  })
  @IsNotEmpty()
  @IsArray()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  tiktokShopIds: number[];

  @ApiPropertyOptional({
    description: 'Number of products to fetch per page (max 100)',
    example: 100,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  pageSize?: number = 100;

  @ApiPropertyOptional({
    description: 'Sort order for fetching products',
    enum: TikTokSortOrder,
    example: TikTokSortOrder.DESC,
  })
  @IsOptional()
  @IsEnum(TikTokSortOrder)
  sortOrder?: TikTokSortOrder = TikTokSortOrder.DESC;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    enum: TikTokProductSortField,
    example: TikTokProductSortField.CREATE_TIME,
  })
  @IsOptional()
  @IsEnum(TikTokProductSortField)
  sortField?: TikTokProductSortField = TikTokProductSortField.CREATE_TIME;

  @ApiPropertyOptional({
    description: 'Maximum number of pages to fetch per shop (1-100)',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Min(1)
  @Max(100)
  maxPages?: number = 10;

  @ApiPropertyOptional({
    description: 'Force full sync (ignore last sync timestamp)',
    example: false,
  })
  @IsOptional()
  forceFullSync?: boolean = false;
}

export class ProductSyncResultDto {
  @ApiProperty({
    description: 'Total number of products processed',
    example: 150,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Number of products created',
    example: 45,
  })
  created: number;

  @ApiProperty({
    description: 'Number of products updated',
    example: 105,
  })
  updated: number;

  @ApiProperty({
    description: 'Number of errors encountered',
    example: 0,
  })
  errors: number;

  @ApiProperty({
    description: 'Timestamp when sync was completed',
    example: '2024-01-15T10:30:00Z',
  })
  syncedAt: string;

  @ApiPropertyOptional({
    description: 'Next page token for continued pagination',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  nextPageToken?: string;

  @ApiPropertyOptional({
    description: 'Detailed error messages',
    example: ['Product 7123456789: Invalid status', 'Product 7123456790: Missing required field'],
  })
  errorMessages?: string[];

  @ApiPropertyOptional({
    description: 'Processing details for each product',
    example: [
      {
        productId: '7123456789',
        status: 'created',
        skusCount: 2,
      },
      {
        productId: '7123456790',
        status: 'updated',
        skusCount: 1,
      },
    ],
  })
  details?: ProductSyncDetailDto[];
}

export class ProductSyncDetailDto {
  @ApiProperty({
    description: 'TikTok product ID',
    example: '7123456789',
  })
  productId: string;

  @ApiProperty({
    description: 'Processing status',
    example: 'created',
    enum: ['created', 'updated', 'skipped', 'error'],
  })
  status: 'created' | 'updated' | 'skipped' | 'error';

  @ApiPropertyOptional({
    description: 'Number of SKUs processed',
    example: 2,
  })
  skusCount?: number;

  @ApiPropertyOptional({
    description: 'Error message if processing failed',
    example: 'Invalid product status',
  })
  errorMessage?: string;

  @ApiPropertyOptional({
    description: 'Internal product ID after processing',
    example: 123,
  })
  internalProductId?: number;
}
