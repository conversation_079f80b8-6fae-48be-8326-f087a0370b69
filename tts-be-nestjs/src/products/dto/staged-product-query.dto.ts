import { IsOptional, IsString, IsDateString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationQueryDto } from '../../common/dto/pagination-query.dto';

/**
 * DTO for querying staged products with pagination, filtering, and sorting
 */
export class StagedProductQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by title (partial match)',
    example: 'T-Shirt',
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({
    description: 'Filter by category ID',
    example: '853000',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by brand ID',
    example: '7082427311584347905',
  })
  @IsOptional()
  @IsString()
  brandId?: string;

  @ApiPropertyOptional({
    description: 'Filter by updated after date',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  updatedAfter?: string;

  @ApiPropertyOptional({
    description: 'Filter by updated before date',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  updatedBefore?: string;
}

/**
 * DTO for staged product with latest upload status
 */
export class StagedProductWithLatestUploadDto {
  @ApiPropertyOptional({ description: 'Staged product data' })
  product: any;

  @ApiPropertyOptional({ description: 'Latest upload status information' })
  latestUpload?: {
    id: number;
    status: string;
    tiktokShopId: number;
    tiktokShopName?: string;
    shopFriendlyName?: string;
    createdAt: Date;
    completedAt?: Date;
  } | null;
}
