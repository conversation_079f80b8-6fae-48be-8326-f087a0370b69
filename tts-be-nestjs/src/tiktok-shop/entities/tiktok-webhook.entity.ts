import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('tiktok_webhook')
export class TikTokWebhook {
  @ApiProperty({
    description: 'Auto-generated primary key',
    example: 1,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'TikTok notification ID from webhook payload',
    example: '7380066284010030890',
  })
  @Column({ unique: true })
  idTT: string;

  @ApiProperty({
    description: 'Notification type (1 = Order Status Change)',
    example: 1,
  })
  @Column('int')
  type: number;

  @ApiProperty({
    description: 'TikTok Shop ID from webhook payload',
    example: '7495540735365777507',
  })
  @Column()
  shop_id: string;

  @ApiProperty({
    description: 'Unix timestamp from webhook payload',
    example: 1718305585,
  })
  @Column('int')
  timestamp: number;

  @ApiProperty({
    description: 'Business parameters from webhook data field',
    example: {
      order_id: '576653688135258178',
      order_status: 'UNPAID',
      is_on_hold_order: true,
      update_time: 1718305585,
    },
  })
  @Column('jsonb', { nullable: true })
  data: any;

  @ApiProperty({
    description: 'Complete raw webhook payload for future extensibility',
  })
  @Column('jsonb')
  rawTikTokResponse: any;

  @ApiProperty({ description: 'Created Date' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'Updated Date' })
  @UpdateDateColumn()
  updatedAt: Date;
}
