import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TikTokWebhook } from '../entities/tiktok-webhook.entity';
import { TikTokShop } from '../entities/tiktok-shop.entity';
import { TikTokApplication } from '../entities/tiktok-application.entity';
import { TikTokWebhookPayloadDto, OrderStatusChangeDataDto } from '../dto/webhook.dto';
import { verifyWebhookSignature } from '../utils/webhook-signature.util';
import { OrdersService } from '../../orders/orders.service';

@Injectable()
export class WebhookService {
  private readonly logger = new Logger(WebhookService.name);

  constructor(
    @InjectRepository(TikTokWebhook)
    private readonly webhookRepository: Repository<TikTokWebhook>,
    @InjectRepository(TikTokShop)
    private readonly tikTokShopRepository: Repository<TikTokShop>,
    @InjectRepository(TikTokApplication)
    private readonly tikTokApplicationRepository: Repository<TikTokApplication>,
    @Inject(forwardRef(() => OrdersService))
    private readonly ordersService: OrdersService,
  ) {}

  /**
   * Verify webhook signature using TikTok Shop signature verification process
   * @param shopId - TikTok Shop ID from webhook payload
   * @param webhookPayload - Raw webhook payload as string
   * @param receivedSignature - Signature from Authorization header
   * @returns Boolean indicating if signature is valid
   */
  async verifySignature(
    shopId: string,
    webhookPayload: string,
    receivedSignature: string,
  ): Promise<boolean> {
    try {
      // Step 1: Find TikTok Shop by shop_id (idTT)
      const tikTokShop = await this.tikTokShopRepository.findOne({
        where: { idTT: shopId },
      });

      if (!tikTokShop) {
        this.logger.warn(`TikTok Shop with idTT ${shopId} not found`);
        return false;
      }

      // Step 2: Find TikTok Application by app_key
      const tikTokApplication = await this.tikTokApplicationRepository.findOne({
        where: { app_key: tikTokShop.app_key },
      });

      if (!tikTokApplication) {
        this.logger.warn(`TikTok Application with app_key ${tikTokShop.app_key} not found`);
        return false;
      }

      // Step 3: Verify signature using app_key and app_secret
      const isValid = verifyWebhookSignature(
        tikTokApplication.app_key,
        webhookPayload,
        tikTokApplication.app_secret,
        receivedSignature,
      );

      if (!isValid) {
        this.logger.warn(`Invalid webhook signature for shop ${shopId}`);
      }

      return isValid;
    } catch (error) {
      this.logger.error(`Error verifying webhook signature: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Save webhook notification to database
   * @param payload - Validated webhook payload
   * @param rawPayload - Complete raw webhook payload
   * @returns Saved webhook entity
   */
  async saveWebhook(
    payload: TikTokWebhookPayloadDto,
    rawPayload: any,
  ): Promise<TikTokWebhook> {
    try {
      const webhook = this.webhookRepository.create({
        idTT: payload.tts_notification_id,
        type: payload.type,
        shop_id: payload.shop_id,
        timestamp: payload.timestamp,
        data: payload.data,
        rawTikTokResponse: rawPayload,
      });

      const savedWebhook = await this.webhookRepository.save(webhook);
      
      this.logger.log(
        `Saved webhook notification ${payload.tts_notification_id} for shop ${payload.shop_id}`,
      );

      return savedWebhook;
    } catch (error) {
      this.logger.error(`Error saving webhook: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process Order Status Change notification (type = 1)
   * @param webhook - Saved webhook entity
   * @param data - Order status change data
   */
  async processOrderStatusChange(
    webhook: TikTokWebhook,
    data: OrderStatusChangeDataDto,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing order status change for order ${data.order_id}, status: ${data.order_status}`,
      );

      // Find the TikTok Shop to get the internal shop ID
      const tikTokShop = await this.tikTokShopRepository.findOne({
        where: { idTT: webhook.shop_id },
      });

      if (!tikTokShop) {
        throw new NotFoundException(`TikTok Shop with idTT ${webhook.shop_id} not found`);
      }

      // Call synchronizeOrderDetails to get updated order information from TikTok API
      // This follows the pattern from the existing synchronizeOrderDetails function
      await this.ordersService.synchronizeOrderDetails(
        {
          tiktokShopId: tikTokShop.id,
          orderIds: [data.order_id],
        },
        tikTokShop.userId, // Use the shop owner's user ID
      );

      this.logger.log(
        `Successfully processed order status change for order ${data.order_id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing order status change for order ${data.order_id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process webhook notification based on type
   * @param webhook - Saved webhook entity
   */
  async processWebhook(webhook: TikTokWebhook): Promise<void> {
    try {
      switch (webhook.type) {
        case 1: // Order Status Change
          if (webhook.data) {
            await this.processOrderStatusChange(webhook, webhook.data as OrderStatusChangeDataDto);
          } else {
            this.logger.warn(`No data found for order status change webhook ${webhook.idTT}`);
          }
          break;

        default:
          this.logger.warn(`Unsupported webhook type: ${webhook.type}`);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Error processing webhook ${webhook.idTT}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
