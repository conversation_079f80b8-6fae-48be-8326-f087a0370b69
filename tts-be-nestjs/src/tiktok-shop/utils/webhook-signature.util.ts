import * as crypto from 'crypto';

/**
 * Generates HMAC-SHA256 signature for TikTok Shop webhook verification
 * Based on TikTok Shop webhook documentation:
 * Signature base string: {app_key}{webhook_payload}
 * Signing key: app_secret
 * 
 * @param appKey - TikTok Shop application key
 * @param webhookPayload - Raw webhook payload as string
 * @param appSecret - TikTok Shop application secret
 * @returns Hexadecimal signature string
 */
export function generateWebhookSignature(
  appKey: string,
  webhookPayload: string,
  appSecret: string,
): string {
  // Step 1: Create signature base string by concatenating app_key and webhook payload
  const signatureBaseString = `${appKey}${webhookPayload}`;

  // Step 2: Generate HMAC-SHA256 signature using app_secret as the key
  const hmac = crypto.createHmac('sha256', appSecret);
  hmac.update(signatureBaseString);
  const signature = hmac.digest('hex');

  return signature;
}

/**
 * Verifies TikTok Shop webhook signature
 * 
 * @param appKey - TikTok Shop application key
 * @param webhookPayload - Raw webhook payload as string
 * @param appSecret - TikTok Shop application secret
 * @param receivedSignature - Signature from Authorization header
 * @returns Boolean indicating if signature is valid
 */
export function verifyWebhookSignature(
  appKey: string,
  webhookPayload: string,
  appSecret: string,
  receivedSignature: string,
): boolean {
  const expectedSignature = generateWebhookSignature(appKey, webhookPayload, appSecret);
  
  // Use crypto.timingSafeEqual to prevent timing attacks
  try {
    const expectedBuffer = Buffer.from(expectedSignature, 'hex');
    const receivedBuffer = Buffer.from(receivedSignature, 'hex');
    
    // Ensure both buffers are the same length
    if (expectedBuffer.length !== receivedBuffer.length) {
      return false;
    }
    
    return crypto.timingSafeEqual(expectedBuffer, receivedBuffer);
  } catch (error) {
    // If there's any error in buffer creation or comparison, signature is invalid
    return false;
  }
}
