import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsString, IsObject, IsOptional, IsBoolean } from 'class-validator';

export class OrderStatusChangeDataDto {
  @ApiProperty({
    description: 'TikTok Shop order identification',
    example: '576653688135258178',
  })
  @IsString()
  order_id: string;

  @ApiProperty({
    description: 'Current order status',
    example: 'UNPAID',
    enum: [
      'UNPAID',
      'ON_HOLD',
      'AWAITING_SHIPMENT',
      'AWAITING_COLLECTION',
      'CANCEL',
      'IN_TRANSIT',
      'DELIVERED',
      'COMPLETED',
    ],
  })
  @IsString()
  order_status: string;

  @ApiProperty({
    description: 'Boolean indicating if order has/will experience ON_HOLD status',
    example: true,
  })
  @IsBoolean()
  is_on_hold_order: boolean;

  @ApiProperty({
    description: 'Unix timestamp of status update',
    example: 1718305585,
  })
  @IsNumber()
  update_time: number;
}

export class TikTokWebhookPayloadDto {
  @ApiProperty({
    description: 'Notification type (1 = Order Status Change)',
    example: 1,
  })
  @IsNumber()
  type: number;

  @ApiProperty({
    description: 'TikTok notification ID',
    example: '7380066284010030890',
  })
  @IsString()
  tts_notification_id: string;

  @ApiProperty({
    description: 'TikTok Shop ID',
    example: '7495540735365777507',
  })
  @IsString()
  shop_id: string;

  @ApiProperty({
    description: 'Unix timestamp',
    example: 1718305585,
  })
  @IsNumber()
  timestamp: number;

  @ApiProperty({
    description: 'Business parameters (varies by notification type)',
    example: {
      order_id: '576653688135258178',
      order_status: 'UNPAID',
      is_on_hold_order: true,
      update_time: 1718305585,
    },
  })
  @IsObject()
  @IsOptional()
  data?: OrderStatusChangeDataDto | any;
}

export class TikTokWebhookResponseDto {
  @ApiProperty({
    description: 'Webhook processing result',
    example: 'success',
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Processing message',
    example: 'Webhook processed successfully',
  })
  @IsString()
  message: string;

  @ApiProperty({
    description: 'Webhook ID for tracking',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  webhookId?: number;
}
