import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TikTokShopController } from './tiktok-shop.controller';
import { TikTokShopService } from './tiktok-shop.service';
import { TikTokShop } from './entities/tiktok-shop.entity';
import { TikTokClientFactory } from './tiktok-client.factory';
import { TikTokApplication } from './entities/tiktok-application.entity';
import { Warehouse } from './entities/warehouse.entity';
import { TikTokWebhook } from './entities/tiktok-webhook.entity';
import { WebhookController } from './controllers/webhook.controller';
import { WebhookService } from './services/webhook.service';
import { OrdersModule } from '../orders/orders.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([TikTokShop, TikTokApplication, Warehouse, TikTokWebhook]),
    forwardRef(() => OrdersModule),
  ],
  controllers: [TikTokShopController, WebhookController],
  providers: [TikTokShopService, TikTokClientFactory, WebhookService],
  exports: [TikTokShopService, TikTokClientFactory, WebhookService],
})
export class TiktokShopModule {}
