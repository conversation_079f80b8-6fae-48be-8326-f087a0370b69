import {
  Controller,
  Post,
  Body,
  Headers,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
  UnauthorizedException,
  InternalServerErrorException,
  Req,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiHeader, ApiBody } from '@nestjs/swagger';
import { Request } from 'express';
import { plainToInstance } from 'class-transformer';
import { validateSync } from 'class-validator';
import { Public } from '../../auth/decorators/public.decorator';
import { WebhookService } from '../services/webhook.service';
import {
  TikTokWebhookPayloadDto,
  TikTokWebhookResponseDto,
} from '../dto/webhook.dto';

@ApiTags('TikTok Shop Webhooks')
@Controller()
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly webhookService: WebhookService) {}

  @Public()
  @Post('webhooktiktok')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'TikTok Shop Webhook Endpoint',
    description: 'Receives webhook notifications from TikTok Shop with signature verification',
  })
  @ApiHeader({
    name: 'Authorization',
    description: 'HMAC-SHA256 signature for webhook verification',
    required: true,
  })
  @ApiBody({
    type: TikTokWebhookPayloadDto,
    description: 'TikTok Shop webhook payload',
  })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
    type: TikTokWebhookResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid webhook payload',
  })
  @ApiResponse({
    status: 401,
    description: 'Invalid signature',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async handleWebhook(
    @Body() body: any,
    @Headers('authorization') authorizationHeader: string,
    @Req() request: Request,
  ): Promise<TikTokWebhookResponseDto> {
    try {
      this.logger.log('Received TikTok Shop webhook notification');

      // Get raw body as string for signature verification
      const rawBody = JSON.stringify(body);
      
      // Validate webhook payload structure
      const validatedPayload = plainToInstance(TikTokWebhookPayloadDto, body);
      const validationErrors = validateSync(validatedPayload);

      if (validationErrors.length > 0) {
        this.logger.warn(`Invalid webhook payload: ${JSON.stringify(validationErrors)}`);
        throw new BadRequestException('Invalid webhook payload structure');
      }

      // Extract signature from Authorization header
      if (!authorizationHeader) {
        this.logger.warn('Missing Authorization header');
        throw new UnauthorizedException('Missing Authorization header');
      }

      // Verify webhook signature
      const isSignatureValid = await this.webhookService.verifySignature(
        validatedPayload.shop_id,
        rawBody,
        authorizationHeader,
      );

      if (!isSignatureValid) {
        this.logger.warn(
          `Invalid webhook signature for shop ${validatedPayload.shop_id}`,
        );
        throw new UnauthorizedException('Invalid webhook signature');
      }

      this.logger.log(
        `Webhook signature verified for shop ${validatedPayload.shop_id}, notification ${validatedPayload.tts_notification_id}`,
      );

      // Save webhook to database
      const savedWebhook = await this.webhookService.saveWebhook(
        validatedPayload,
        body,
      );

      // Process webhook based on type
      await this.webhookService.processWebhook(savedWebhook);

      this.logger.log(
        `Successfully processed webhook ${validatedPayload.tts_notification_id}`,
      );

      return {
        status: 'success',
        message: 'Webhook processed successfully',
        webhookId: savedWebhook.id,
      };
    } catch (error) {
      this.logger.error(`Error processing webhook: ${error.message}`, error.stack);

      if (error instanceof BadRequestException || error instanceof UnauthorizedException) {
        throw error;
      }

      throw new InternalServerErrorException('Failed to process webhook');
    }
  }
}
