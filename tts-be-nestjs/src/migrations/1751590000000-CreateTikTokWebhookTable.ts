import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTikTokWebhookTable1751590000000 implements MigrationInterface {
    name = 'CreateTikTokWebhookTable1751590000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create tiktok_webhook table
        await queryRunner.query(`CREATE TABLE "tiktok_webhook" (
            "id" SERIAL NOT NULL, 
            "idTT" character varying NOT NULL, 
            "type" integer NOT NULL, 
            "shop_id" character varying NOT NULL, 
            "timestamp" integer NOT NULL, 
            "data" jsonb, 
            "rawTikTokResponse" jsonb NOT NULL, 
            "createdAt" TIMESTAMP NOT NULL DEFAULT now(), 
            "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), 
            CONSTRAINT "UQ_tiktok_webhook_id_tt" UNIQUE ("idTT"), 
            CONSTRAINT "PK_tiktok_webhook_id" PRIMARY KEY ("id")
        )`);

        // Create indexes for performance
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_id_tt" ON "tiktok_webhook" ("idTT")`);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_shop_id" ON "tiktok_webhook" ("shop_id")`);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_type" ON "tiktok_webhook" ("type")`);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_timestamp" ON "tiktok_webhook" ("timestamp")`);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_created_at" ON "tiktok_webhook" ("createdAt")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_timestamp"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_type"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_shop_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_id_tt"`);

        // Drop table
        await queryRunner.query(`DROP TABLE "tiktok_webhook"`);
    }
}
