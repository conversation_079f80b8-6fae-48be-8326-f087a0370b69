import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDocUrlAndTrackingNumberColumns1754712376328 implements MigrationInterface {
    name = 'AddDocUrlAndTrackingNumberColumns1754712376328'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD "docUrl" character varying`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD "trackingNumber" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP COLUMN "trackingNumber"`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP COLUMN "docUrl"`);
    }

}
