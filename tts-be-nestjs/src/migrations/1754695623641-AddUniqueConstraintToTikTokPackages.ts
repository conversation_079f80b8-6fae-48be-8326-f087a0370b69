import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUniqueConstraintToTikTokPackages1754695623641 implements MigrationInterface {
    name = 'AddUniqueConstraintToTikTokPackages1754695623641'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add unique constraint on packageIdTT (TikTok Package ID should be unique)
        await queryRunner.query(`
            ALTER TABLE "tiktok_packages"
            ADD CONSTRAINT "UQ_tiktok_packages_package_id_tt"
            UNIQUE ("packageIdTT")
        `);

        // Add unique constraint on combination of orderId and orderIdTT
        // (one order can have multiple packages, but each orderIdTT should be unique per orderId)
        await queryRunner.query(`
            ALTER TABLE "tiktok_packages"
            ADD CONSTRAINT "UQ_tiktok_packages_order_combination"
            UNIQUE ("orderId", "orderIdTT")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop unique constraints
        await queryRunner.query(`
            ALTER TABLE "tiktok_packages"
            DROP CONSTRAINT "UQ_tiktok_packages_order_combination"
        `);

        await queryRunner.query(`
            ALTER TABLE "tiktok_packages"
            DROP CONSTRAINT "UQ_tiktok_packages_package_id_tt"
        `);
    }

}
