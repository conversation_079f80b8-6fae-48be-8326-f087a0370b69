import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPackagesToOrders1751600000000 implements MigrationInterface {
  name = 'AddPackagesToOrders1751600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      ADD "packages" jsonb
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "orders" 
      DROP COLUMN "packages"
    `);
  }
}
