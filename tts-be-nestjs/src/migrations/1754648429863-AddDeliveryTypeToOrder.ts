import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDeliveryTypeToOrder1754648429863 implements MigrationInterface {
    name = 'AddDeliveryTypeToOrder1754648429863'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "order_line_items" DROP CONSTRAINT "FK_order_line_items_order_id"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_tiktok_shop_id"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_orders_user_id"`);
        await queryRunner.query(`ALTER TABLE "product_uploads" DROP CONSTRAINT "FK_product_uploads_tiktok_shop_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_shop_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_type"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_timestamp"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_webhook_created_at"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_order_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_product_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_sku_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_seller_sku"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_order_line_items_package_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_tiktok_shop_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_status"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_create_time_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_paid_time"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_orders_update_time_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_product_uploads_tiktok_shop_id"`);
        await queryRunner.query(`ALTER TABLE "orders" ADD "deliveryType" character varying`);
        await queryRunner.query(`ALTER TABLE "order_line_items" ADD CONSTRAINT "FK_87e26f6441c7787271dcde83051" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_1f68ca823f7ab610d6fbbb84ddc" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_151b79a83ba240b0cb31b2302d1" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "product_uploads" ADD CONSTRAINT "FK_61c5be3c4040b289b3a1657fa29" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "product_uploads" DROP CONSTRAINT "FK_61c5be3c4040b289b3a1657fa29"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_151b79a83ba240b0cb31b2302d1"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP CONSTRAINT "FK_1f68ca823f7ab610d6fbbb84ddc"`);
        await queryRunner.query(`ALTER TABLE "order_line_items" DROP CONSTRAINT "FK_87e26f6441c7787271dcde83051"`);
        await queryRunner.query(`ALTER TABLE "orders" DROP COLUMN "deliveryType"`);
        await queryRunner.query(`CREATE INDEX "IDX_product_uploads_tiktok_shop_id" ON "product_uploads" ("tiktokShopId") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_update_time_tt" ON "orders" ("updateTimeTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_paid_time" ON "orders" ("paidTime") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_user_id" ON "orders" ("userId") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_create_time_tt" ON "orders" ("createTimeTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_status" ON "orders" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_tiktok_shop_id" ON "orders" ("tiktokShopId") `);
        await queryRunner.query(`CREATE INDEX "IDX_orders_id_tt" ON "orders" ("idTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_package_id" ON "order_line_items" ("packageId") `);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_seller_sku" ON "order_line_items" ("sellerSku") `);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_sku_id_tt" ON "order_line_items" ("skuIdTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_product_id_tt" ON "order_line_items" ("productIdTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_order_line_items_order_id" ON "order_line_items" ("orderId") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_created_at" ON "tiktok_webhook" ("createdAt") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_timestamp" ON "tiktok_webhook" ("timestamp") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_type" ON "tiktok_webhook" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_shop_id" ON "tiktok_webhook" ("shop_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_webhook_id_tt" ON "tiktok_webhook" ("idTT") `);
        await queryRunner.query(`ALTER TABLE "product_uploads" ADD CONSTRAINT "FK_product_uploads_tiktok_shop_id" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_user_id" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "orders" ADD CONSTRAINT "FK_orders_tiktok_shop_id" FOREIGN KEY ("tiktokShopId") REFERENCES "tiktok_shops"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "order_line_items" ADD CONSTRAINT "FK_order_line_items_order_id" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

}
