import { MigrationInterface, QueryRunner } from "typeorm";

export class AddRawTikTokResponseDocumentColumn1754712015696 implements MigrationInterface {
    name = 'AddRawTikTokResponseDocumentColumn1754712015696'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_packages_order_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_packages_user_id"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_packages_package_id_tt"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_tiktok_packages_order_id_tt"`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "UQ_tiktok_packages_order_combination"`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD "rawTikTokResponseDocument" jsonb`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP CONSTRAINT "UQ_tiktok_packages_package_id_tt"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD CONSTRAINT "UQ_tiktok_packages_package_id_tt" UNIQUE ("packageIdTT")`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" DROP COLUMN "rawTikTokResponseDocument"`);
        await queryRunner.query(`ALTER TABLE "tiktok_packages" ADD CONSTRAINT "UQ_tiktok_packages_order_combination" UNIQUE ("orderIdTT", "orderId")`);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_packages_order_id_tt" ON "tiktok_packages" ("orderIdTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_packages_package_id_tt" ON "tiktok_packages" ("packageIdTT") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_packages_user_id" ON "tiktok_packages" ("userId") `);
        await queryRunner.query(`CREATE INDEX "IDX_tiktok_packages_order_id" ON "tiktok_packages" ("orderId") `);
    }

}
