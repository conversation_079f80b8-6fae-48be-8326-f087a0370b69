/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderListResponseDataOrders } from './GetOrderListResponseDataOrders';

export class Order202309GetOrderListResponseData {
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * Order information.
    */
    'orders'?: Array<Order202309GetOrderListResponseDataOrders>;
    /**
    * Total number of orders in the search result.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<Order202309GetOrderListResponseDataOrders>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseData.attributeTypeMap;
    }
}

