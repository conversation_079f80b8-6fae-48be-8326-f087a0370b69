/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderDetailResponseDataOrders } from './GetOrderDetailResponseDataOrders';

export class Order202309GetOrderDetailResponseData {
    /**
    * Order information.
    */
    'orders'?: Array<Order202309GetOrderDetailResponseDataOrders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<Order202309GetOrderDetailResponseDataOrders>"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderDetailResponseData.attributeTypeMap;
    }
}

