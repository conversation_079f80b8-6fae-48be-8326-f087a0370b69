/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences {
    /**
    * Drop-off location selected by the recipient.
    */
    'dropOffLocation'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dropOffLocation",
            "baseName": "drop_off_location",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences.attributeTypeMap;
    }
}

