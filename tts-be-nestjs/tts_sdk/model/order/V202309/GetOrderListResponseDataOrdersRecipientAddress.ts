/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences } from './GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences';
import { Order202309GetOrderListResponseDataOrdersRecipientAddressDistrictInfo } from './GetOrderListResponseDataOrdersRecipientAddressDistrictInfo';

export class Order202309GetOrderListResponseDataOrdersRecipientAddress {
    /**
    * Full recipient detailed address.
    */
    'addressDetail'?: string;
    /**
    * The first line of the street address.
    */
    'addressLine1'?: string;
    /**
    * The second line of the street address.
    */
    'addressLine2'?: string;
    /**
    * The third line of the street address. Usually only for the Brazilian market
    */
    'addressLine3'?: string;
    /**
    * The fourth line of the street address. Usually only for the Brazilian market
    */
    'addressLine4'?: string;
    'deliveryPreferences'?: Order202309GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences;
    /**
    * `district_info` is unavailable under `UNPAID` and `ON_HOLD` statuses.
    */
    'districtInfo'?: Array<Order202309GetOrderListResponseDataOrdersRecipientAddressDistrictInfo>;
    /**
    * Recipient first name.  If the recipient first and last names are not provided separately, this parameter will have the same value as the `name` parameter.   **Note**: Only available in US and UK markets.
    */
    'firstName'?: string;
    /**
    * Recipient first name in katakana. **Note**: Applicable only for the JP market.
    */
    'firstNameLocalScript'?: string;
    /**
    * Complete recipient address information.
    */
    'fullAddress'?: string;
    /**
    * Recipient last name.   If the recipient first and last names are not provided separately, this parameter will be empty.   **Note**: Only available in US and UK markets.
    */
    'lastName'?: string;
    /**
    * Recipient last name in katakana. **Note**: Applicable only for the JP market.
    */
    'lastNameLocalScript'?: string;
    /**
    * Recipient name.  **Note**: If this order uses platform logistics, the recipient name will be desensitized.
    */
    'name'?: string;
    /**
    * Recipient telephone number.  **Note**: If this order uses platform logistics, the phone number will be desensitized.
    */
    'phoneNumber'?: string;
    /**
    * The postal code that can be used by seller for shipping. For the US market, this refers to the ZIP Code.
    */
    'postalCode'?: string;
    /**
    * Region code.
    */
    'regionCode'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressDetail",
            "baseName": "address_detail",
            "type": "string"
        },
        {
            "name": "addressLine1",
            "baseName": "address_line1",
            "type": "string"
        },
        {
            "name": "addressLine2",
            "baseName": "address_line2",
            "type": "string"
        },
        {
            "name": "addressLine3",
            "baseName": "address_line3",
            "type": "string"
        },
        {
            "name": "addressLine4",
            "baseName": "address_line4",
            "type": "string"
        },
        {
            "name": "deliveryPreferences",
            "baseName": "delivery_preferences",
            "type": "Order202309GetOrderListResponseDataOrdersRecipientAddressDeliveryPreferences"
        },
        {
            "name": "districtInfo",
            "baseName": "district_info",
            "type": "Array<Order202309GetOrderListResponseDataOrdersRecipientAddressDistrictInfo>"
        },
        {
            "name": "firstName",
            "baseName": "first_name",
            "type": "string"
        },
        {
            "name": "firstNameLocalScript",
            "baseName": "first_name_local_script",
            "type": "string"
        },
        {
            "name": "fullAddress",
            "baseName": "full_address",
            "type": "string"
        },
        {
            "name": "lastName",
            "baseName": "last_name",
            "type": "string"
        },
        {
            "name": "lastNameLocalScript",
            "baseName": "last_name_local_script",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "phoneNumber",
            "baseName": "phone_number",
            "type": "string"
        },
        {
            "name": "postalCode",
            "baseName": "postal_code",
            "type": "string"
        },
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersRecipientAddress.attributeTypeMap;
    }
}

