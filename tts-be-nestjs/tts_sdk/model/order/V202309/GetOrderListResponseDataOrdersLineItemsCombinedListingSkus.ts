/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListResponseDataOrdersLineItemsCombinedListingSkus {
    /**
    * The original product ID related to the combined listing SKU.
    */
    'productId'?: string;
    /**
    * The original seller SKU (which is defined by the seller) related to the combined listing SKU.
    */
    'sellerSku'?: string;
    /**
    * The quantity of original SKUs that compose the combined listing SKU.
    */
    'skuCount'?: number;
    /**
    * The original SKU ID related to the combined listing SKU.
    */
    'skuId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "productId",
            "baseName": "product_id",
            "type": "string"
        },
        {
            "name": "sellerSku",
            "baseName": "seller_sku",
            "type": "string"
        },
        {
            "name": "skuCount",
            "baseName": "sku_count",
            "type": "number"
        },
        {
            "name": "skuId",
            "baseName": "sku_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersLineItemsCombinedListingSkus.attributeTypeMap;
    }
}

