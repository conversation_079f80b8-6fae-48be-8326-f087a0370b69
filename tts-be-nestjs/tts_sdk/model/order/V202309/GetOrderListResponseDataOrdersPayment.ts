/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListResponseDataOrdersPayment {
    /**
    * A service fee is charged on every transaction made. The charge is applied from the fifth order onwards and collected directly from customers during checkout. Only available in the Indonesia market.
    */
    'buyerServiceFee'?: string;
    /**
    * Currency for payment.
    */
    'currency'?: string;
    /**
    * A fee charged to the buyer to cover the additional processing/handling costs associated with the chosen payment method.  **Note**: Only available in the Indonesia market.
    */
    'handlingFee'?: string;
    /**
    * The cost incurred by the buyers for coverage against defects or damage to the product after purchase.  **Note**: Only available in the Indonesia market.
    */
    'itemInsuranceFee'?: string;
    /**
    * Shipping fee before discount.
    */
    'originalShippingFee'?: string;
    /**
    * Total original price of the products (VAT included for cross-border shops).  For the US market, this is pre-tax total amount.
    */
    'originalTotalProductPrice'?: string;
    /**
    * Product discount by platform.
    */
    'platformDiscount'?: string;
    /**
    * The tax on the total item price.
    */
    'productTax'?: string;
    /**
    * Retail delivery fee (RDF).   **Note**: Only available in the US market.
    */
    'retailDeliveryFee'?: string;
    /**
    * Product discount by seller.
    */
    'sellerDiscount'?: string;
    /**
    * Buyer paid shipping fee.   `shipping_fee = original_shipping_fee - shipping_fee_seller_discount - shipping_fee_platform_discount`  For the US market, this is pre-tax total amount.
    */
    'shippingFee'?: string;
    /**
    * Shipping fee discount provided by seller, eligible for co-funded reimbursement upon order delivery, based on Co-Funded Free Shipping program terms. **Note**: This will be 0 for orders that did not meet minimum order value for co-funded reimbursement. In this case, refer to `shipping_fee_seller_discount` for the shipping discount the buyer received.
    */
    'shippingFeeCofundedDiscount'?: string;
    /**
    * Shipping fee discount provided by platform.
    */
    'shippingFeePlatformDiscount'?: string;
    /**
    * Shipping fee discount provided by seller for an order that will not qualify for co-funded reimbursement. **Note**: If an order meets the minimum order value for co-funded reimbursement, this will be 0. In this case, refer to `shipping_fee_cofunded_discount` for the shipping discount the buyer received.
    */
    'shippingFeeSellerDiscount'?: string;
    /**
    * The tax on the shipping price.
    */
    'shippingFeeTax'?: string;
    /**
    * The cost incurred by the buyer for coverage against loss or damage to goods during transit.  **Note**: Only available in the Indonesia market.
    */
    'shippingInsuranceFee'?: string;
    /**
    * Small order fee for TH (**Thailand market only**).   Small order fee means that the platform will set a minimum order spending amount. When the order amount is lower than the minimum order spending amount, the user needs to pay a small order fee to meet the platform minimum spending amount.   e.g. Minimum order spending amount is 100, order amount is 80. So the small order fee will be 20.
    */
    'smallOrderFee'?: string;
    /**
    * Buyer paid sub-total of all the SKUs in the order.  `sub_total = original_total_product_price - seller_discount - platform_discount`  For the US market, this is pre-tax total amount.
    */
    'subTotal'?: string;
    /**
    * Buyer paid total taxes for the order. Applicable to both cross-border shops and the US market.
    */
    'tax'?: string;
    /**
    * Buyer paid total payment.  `total_amount = sub_total + shipping_fee + taxes + retail_delivery_fee`
    */
    'totalAmount'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "buyerServiceFee",
            "baseName": "buyer_service_fee",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "handlingFee",
            "baseName": "handling_fee",
            "type": "string"
        },
        {
            "name": "itemInsuranceFee",
            "baseName": "item_insurance_fee",
            "type": "string"
        },
        {
            "name": "originalShippingFee",
            "baseName": "original_shipping_fee",
            "type": "string"
        },
        {
            "name": "originalTotalProductPrice",
            "baseName": "original_total_product_price",
            "type": "string"
        },
        {
            "name": "platformDiscount",
            "baseName": "platform_discount",
            "type": "string"
        },
        {
            "name": "productTax",
            "baseName": "product_tax",
            "type": "string"
        },
        {
            "name": "retailDeliveryFee",
            "baseName": "retail_delivery_fee",
            "type": "string"
        },
        {
            "name": "sellerDiscount",
            "baseName": "seller_discount",
            "type": "string"
        },
        {
            "name": "shippingFee",
            "baseName": "shipping_fee",
            "type": "string"
        },
        {
            "name": "shippingFeeCofundedDiscount",
            "baseName": "shipping_fee_cofunded_discount",
            "type": "string"
        },
        {
            "name": "shippingFeePlatformDiscount",
            "baseName": "shipping_fee_platform_discount",
            "type": "string"
        },
        {
            "name": "shippingFeeSellerDiscount",
            "baseName": "shipping_fee_seller_discount",
            "type": "string"
        },
        {
            "name": "shippingFeeTax",
            "baseName": "shipping_fee_tax",
            "type": "string"
        },
        {
            "name": "shippingInsuranceFee",
            "baseName": "shipping_insurance_fee",
            "type": "string"
        },
        {
            "name": "smallOrderFee",
            "baseName": "small_order_fee",
            "type": "string"
        },
        {
            "name": "subTotal",
            "baseName": "sub_total",
            "type": "string"
        },
        {
            "name": "tax",
            "baseName": "tax",
            "type": "string"
        },
        {
            "name": "totalAmount",
            "baseName": "total_amount",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersPayment.attributeTypeMap;
    }
}

