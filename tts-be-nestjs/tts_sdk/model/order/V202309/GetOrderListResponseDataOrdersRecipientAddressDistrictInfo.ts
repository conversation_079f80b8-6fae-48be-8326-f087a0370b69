/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListResponseDataOrdersRecipientAddressDistrictInfo {
    /**
    * Administrative district level code. Value can be L0/L1/L2/L3/L4. eg. US is L0.
    */
    'addressLevel'?: string;
    /**
    * The name of administrative division that can be used by the seller for shipping. e.g. state/county/city/district/town/etc.
    */
    'addressLevelName'?: string;
    /**
    * Administrative area name. eg: London.
    */
    'addressName'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressLevel",
            "baseName": "address_level",
            "type": "string"
        },
        {
            "name": "addressLevelName",
            "baseName": "address_level_name",
            "type": "string"
        },
        {
            "name": "addressName",
            "baseName": "address_name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersRecipientAddressDistrictInfo.attributeTypeMap;
    }
}

