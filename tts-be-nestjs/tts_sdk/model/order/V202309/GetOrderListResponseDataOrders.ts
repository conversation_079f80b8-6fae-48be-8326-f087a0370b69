/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202309GetOrderListResponseDataOrdersHandlingDuration } from './GetOrderListResponseDataOrdersHandlingDuration';
import { Order202309GetOrderListResponseDataOrdersLineItems } from './GetOrderListResponseDataOrdersLineItems';
import { Order202309GetOrderListResponseDataOrdersPackages } from './GetOrderListResponseDataOrdersPackages';
import { Order202309GetOrderListResponseDataOrdersPayment } from './GetOrderListResponseDataOrdersPayment';
import { Order202309GetOrderListResponseDataOrdersRecipientAddress } from './GetOrderListResponseDataOrdersRecipientAddress';

export class Order202309GetOrderListResponseDataOrders {
    /**
    * An identifier assigned to orders from the same customer during a LIVE session to facilitate combined order shipping when \"auto-combination\" is activated in Seller Center.
    */
    'autoCombineGroupId'?: string;
    /**
    * The anonymized email address of the buyer. It is not recommended to send messages directly to this email address. If you need to contact the buyer, please go to the TikTok Shop Seller Center - Buyer Messages page.
    */
    'buyerEmail'?: string;
    /**
    * The note from the buyer.
    */
    'buyerMessage'?: string;
    /**
    * The automatic cancellation time for orders specified by the platform. Unix timestamp.
    */
    'cancelOrderSlaTime'?: number;
    /**
    * The cancellation reason. Please visit [this appendix](https://partner.tiktokshop.com/docv2/page/650b28280fcef602bf435096) for more information.
    */
    'cancelReason'?: string;
    /**
    * The time an order\'s status was updated to `CANCELLED`. Unix timestamp.
    */
    'cancelTime'?: number;
    /**
    * Cancellation request initiator.  Available values:  - `SELLER` - `BUYER` - `SYSTEM` 
    */
    'cancellationInitiator'?: string;
    /**
    * If the order hasn\'t updated its status to `IN_TRANSIT` before this time, the order will be canceled by TikTok Shop. Unix timestamp.
    */
    'collectionDueTime'?: number;
    /**
    * The time an order\'s status has been updated to `IN_TRANSIT`. Unix timestamp.
    */
    'collectionTime'?: number;
    /**
    * The platform where the order was placed. Possible values: - `TIKTOK_SHOP` - `TOKOPEDIA`  **Note**: Available only in the Indonesia market.
    */
    'commercePlatform'?: string;
    /**
    * CPF (invoice number), used to issue an invoice.  **Note**: Only available in the Brazil market.
    */
    'cpf'?: string;
    /**
    * Name belonging to the CPF number for the Brazil market. 
    */
    'cpfName'?: string;
    /**
    * The date and time that the order was created. Unix timestamp.
    */
    'createTime'?: number;
    /**
    * If the order hasn\'t updated its status to `DELIVERED` before this time, the order will be automatically canceled by TikTok Shop. Unix timestamp.
    */
    'deliveryDueTime'?: number;
    /**
    * Delivery option ID is mapped to seller configured logistics templates ID.
    */
    'deliveryOptionId'?: string;
    /**
    * Delivery option name. For display purposes only. Available values: - `Economy Shipping` - `Standard Shipping` - `Express Shipping`
    */
    'deliveryOptionName'?: string;
    /**
    *  Order should be delivered before this time. Unix timestamp.
    */
    'deliveryOptionRequiredDeliveryTime'?: number;
    /**
    * Order should arrive by this date to be considered on-time and to avoid late delivery penalties.
    */
    'deliverySlaTime'?: number;
    /**
    * The time an order\'s status changed to `DELIVERED`. Unix timestamp.
    */
    'deliveryTime'?: number;
    /**
    * Indicates whether it is a Pick-Up Drop-Off (PUDO) location. The PUDO location is selected by the buyer when placing orders. Available values: - `HOME_DELIVERY`: not a PUDO location - `COLLECTION_POINT`: a PUDO location
    */
    'deliveryType'?: string;
    /**
    * If the order is an exchange order, this field returns the original order\'s order ID, from which the exchange order was generated.   Returned only if is_exchange_order = true.  Note: Only available in US and UK.
    */
    'exchangeSourceOrderId'?: string;
    /**
    * The latest collection time to gain incentives of NDD (Next Day Delivery) project. Unix timestamp.  **Note**: Only available in Thailand and the Philippines.
    */
    'fastDispatchSlaTime'?: number;
    /**
    * Fulfillment type. Only orders with fulfillment type can be shipped by sellers. Available values: - `FULFILLMENT_BY_SELLER`: a method where sellers fulfill orders directly from their own inventory, without using TikTok\'s fulfillment centers. In this model, the seller is responsible for storing, packaging, and shipping the products to customers. - `FULFILLMENT_BY_TIKTOK`: a service offered by TikTok where sellers can send their products to TikTok\'s fulfillment centers. TikTok then takes care of storing, picking, packing, and shipping the products to customers. - `FULFILLMENT_BY_DILAYANI_TOKOPEDIA`: a method where Tokopedia GoTo Logistics provides warehousing and logistics services to sellers and charges a fee for the service.
    */
    'fulfillmentType'?: string;
    'handlingDuration'?: Order202309GetOrderListResponseDataOrdersHandlingDuration;
    /**
    * Whether the recipient address has been updated or changed.
    */
    'hasUpdatedRecipientAddress'?: boolean;
    /**
    * TikTok Shop order ID.
    */
    'id'?: string;
    /**
    * Whether the buyer has a pending cancellation request.
    */
    'isBuyerRequestCancel'?: boolean;
    /**
    * This option is for sellers that accept cash payment on delivery (COD). Buyers will pay in cash upon receiving the package.   Default: FALSE  Only applicable to countries where COD is supported.
    */
    'isCod'?: boolean;
    /**
    * When TRUE, this is an exchange order.  Note: Only available in US and UK.
    */
    'isExchangeOrder'?: boolean;
    /**
    * Indicates whether the order has been changed to or will be updated to `ON_HOLD` status.
    */
    'isOnHoldOrder'?: boolean;
    /**
    * Whether this is a replacement order.
    */
    'isReplacementOrder'?: boolean;
    /**
    * Use this field to determine whether the order is a sample order.
    */
    'isSampleOrder'?: boolean;
    /**
    * Line item info list.
    */
    'lineItems'?: Array<Order202309GetOrderListResponseDataOrdersLineItems>;
    /**
    * Does an order invoice need to be uploaded? Available values: - `UNKNOWN` - `NEED_INVOICE` - `NO_NEED` - `INVOICE_UPLOADED` **Note**: Only available in the Brazil market.
    */
    'needUploadInvoice'?: string;
    /**
    * The order type. Possible values based on region:  **All regions** - `ZERO_LOTTERY`: An order placed during a lottery event in TikTok LIVE.  **US** - `PRE_ORDER`: An advance order for items that are not yet available or released. Fulfillment starts on a specific date in the future. - `MADE_TO_ORDER`: An order for items that are produced only after the order is received. Fulfillment starts after the product is produced. - `BACK_ORDER`: An order for items that are out of stock but expected to be restocked. Fulfillment starts after the product is restocked.  Returns an empty value for standard orders or other types that don\'t fall into the above categories.
    */
    'orderType'?: string;
    /**
    * List of packages included in this order.
    */
    'packages'?: Array<Order202309GetOrderListResponseDataOrdersPackages>;
    /**
    * The date and time that the order was paid. Unix timestamp.
    */
    'paidTime'?: number;
    'payment'?: Order202309GetOrderListResponseDataOrdersPayment;
    /**
    * Payment method name, for display purposes.
    */
    'paymentMethodName'?: string;
    /**
    * To avoid LDR, you must ensure the package is picked up by this time. Only applicable in South East Asia regions. 
    */
    'pickUpCutOffTime'?: number;
    'recipientAddress'?: Order202309GetOrderListResponseDataOrdersRecipientAddress;
    /**
    * The date on which order handling starts and the status of the order changes to [`AWAITING_SHIPMENT`](https://partner.tiktokshop.com/docv2/page/650b1b4bbace3e02b76d1011).  Applicable only if the `order_type` is `PRE_ORDER`.
    */
    'releaseDate'?: number;
    /**
    * The order Id for the order that is being replaced.   Returned only if `is_replacement_order = true`.
    */
    'replacedOrderId'?: string;
    /**
    * Buyer request cancellation time. Unix timestamp.
    */
    'requestCancelTime'?: number;
    /**
    * The latest shipping time specified by the platform. Unix timestamp.
    */
    'rtsSlaTime'?: number;
    /**
    * The time sellers shipped the order (called [Ship Package API](https://partner.tiktokshop.com/docv2/page/650aa4f1defece02be6e7cb1) successfully). Unix timestamp.
    */
    'rtsTime'?: number;
    /**
    * The seller note from TikTok Shop Seller Center.
    */
    'sellerNote'?: string;
    /**
    * If the order hasn\'t updated its status to `AWAITING_COLLECTION` before this time, the order will be automatically canceled by TikTok Shop. Unix timestamp.
    */
    'shippingDueTime'?: number;
    /**
    * The name of the current shipping provider.
    */
    'shippingProvider'?: string;
    /**
    * The ID of the current shipping provider.
    */
    'shippingProviderId'?: string;
    /**
    * Delivery method. Available values: - `TIKTOK`: Shipping service provided by TikTok. The seller should obtain shipping label from TikTok. - `SELLER`: Seller provides shipping, including through 3rd party fulfillment providers on behalf of the seller.
    */
    'shippingType'?: string;
    /**
    * Indicates whether the order is combined or split: - `COMBINED` - `SPLIT`  This field will be used in future fulfillment APIs.
    */
    'splitOrCombineTag'?: string;
    /**
    * Specific order status. Available values: - `UNPAID`: The order has been placed, but payment has not been completed. - `ON_HOLD`: The order has been accepted and is awaiting fulfillment. The buyer may still cancel without the seller’s approval. If `order_type=PRE_ORDER`, the product is still awaiting release so payment will only be authorized 1 day before the release, but the seller should start preparing for the release. (Applicable only for the US and UK market). - `AWAITING_SHIPMENT`: The order is ready to be shipped, but no items have been shipped yet. - `PARTIALLY_SHIPPING`: Some items in the order have been shipped, but not all. - `AWAITING_COLLECTION`: Shipping has been arranged, but the package is waiting to be collected by the carrier. - `IN_TRANSIT`: The package has been collected by the carrier and delivery is in progress. - `DELIVERED`: The package has been delivered to the buyer. - `COMPLETED`: The order has been completed, and no further returns or refunds are allowed. - `CANCELLED`: The order has been cancelled.
    */
    'status'?: string;
    /**
    * Tracking number. Available after the package has been shipped.
    */
    'trackingNumber'?: string;
    /**
    * The latest collection time specified by the platform. Unix timestamp.
    */
    'ttsSlaTime'?: number;
    /**
    * Time of order status change. Unix timestamp.
    */
    'updateTime'?: number;
    /**
    * Buyer user ID.
    */
    'userId'?: string;
    /**
    * Seller warehouse ID.
    */
    'warehouseId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "autoCombineGroupId",
            "baseName": "auto_combine_group_id",
            "type": "string"
        },
        {
            "name": "buyerEmail",
            "baseName": "buyer_email",
            "type": "string"
        },
        {
            "name": "buyerMessage",
            "baseName": "buyer_message",
            "type": "string"
        },
        {
            "name": "cancelOrderSlaTime",
            "baseName": "cancel_order_sla_time",
            "type": "number"
        },
        {
            "name": "cancelReason",
            "baseName": "cancel_reason",
            "type": "string"
        },
        {
            "name": "cancelTime",
            "baseName": "cancel_time",
            "type": "number"
        },
        {
            "name": "cancellationInitiator",
            "baseName": "cancellation_initiator",
            "type": "string"
        },
        {
            "name": "collectionDueTime",
            "baseName": "collection_due_time",
            "type": "number"
        },
        {
            "name": "collectionTime",
            "baseName": "collection_time",
            "type": "number"
        },
        {
            "name": "commercePlatform",
            "baseName": "commerce_platform",
            "type": "string"
        },
        {
            "name": "cpf",
            "baseName": "cpf",
            "type": "string"
        },
        {
            "name": "cpfName",
            "baseName": "cpf_name",
            "type": "string"
        },
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "deliveryDueTime",
            "baseName": "delivery_due_time",
            "type": "number"
        },
        {
            "name": "deliveryOptionId",
            "baseName": "delivery_option_id",
            "type": "string"
        },
        {
            "name": "deliveryOptionName",
            "baseName": "delivery_option_name",
            "type": "string"
        },
        {
            "name": "deliveryOptionRequiredDeliveryTime",
            "baseName": "delivery_option_required_delivery_time",
            "type": "number"
        },
        {
            "name": "deliverySlaTime",
            "baseName": "delivery_sla_time",
            "type": "number"
        },
        {
            "name": "deliveryTime",
            "baseName": "delivery_time",
            "type": "number"
        },
        {
            "name": "deliveryType",
            "baseName": "delivery_type",
            "type": "string"
        },
        {
            "name": "exchangeSourceOrderId",
            "baseName": "exchange_source_order_id",
            "type": "string"
        },
        {
            "name": "fastDispatchSlaTime",
            "baseName": "fast_dispatch_sla_time",
            "type": "number"
        },
        {
            "name": "fulfillmentType",
            "baseName": "fulfillment_type",
            "type": "string"
        },
        {
            "name": "handlingDuration",
            "baseName": "handling_duration",
            "type": "Order202309GetOrderListResponseDataOrdersHandlingDuration"
        },
        {
            "name": "hasUpdatedRecipientAddress",
            "baseName": "has_updated_recipient_address",
            "type": "boolean"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isBuyerRequestCancel",
            "baseName": "is_buyer_request_cancel",
            "type": "boolean"
        },
        {
            "name": "isCod",
            "baseName": "is_cod",
            "type": "boolean"
        },
        {
            "name": "isExchangeOrder",
            "baseName": "is_exchange_order",
            "type": "boolean"
        },
        {
            "name": "isOnHoldOrder",
            "baseName": "is_on_hold_order",
            "type": "boolean"
        },
        {
            "name": "isReplacementOrder",
            "baseName": "is_replacement_order",
            "type": "boolean"
        },
        {
            "name": "isSampleOrder",
            "baseName": "is_sample_order",
            "type": "boolean"
        },
        {
            "name": "lineItems",
            "baseName": "line_items",
            "type": "Array<Order202309GetOrderListResponseDataOrdersLineItems>"
        },
        {
            "name": "needUploadInvoice",
            "baseName": "need_upload_invoice",
            "type": "string"
        },
        {
            "name": "orderType",
            "baseName": "order_type",
            "type": "string"
        },
        {
            "name": "packages",
            "baseName": "packages",
            "type": "Array<Order202309GetOrderListResponseDataOrdersPackages>"
        },
        {
            "name": "paidTime",
            "baseName": "paid_time",
            "type": "number"
        },
        {
            "name": "payment",
            "baseName": "payment",
            "type": "Order202309GetOrderListResponseDataOrdersPayment"
        },
        {
            "name": "paymentMethodName",
            "baseName": "payment_method_name",
            "type": "string"
        },
        {
            "name": "pickUpCutOffTime",
            "baseName": "pick_up_cut_off_time",
            "type": "number"
        },
        {
            "name": "recipientAddress",
            "baseName": "recipient_address",
            "type": "Order202309GetOrderListResponseDataOrdersRecipientAddress"
        },
        {
            "name": "releaseDate",
            "baseName": "release_date",
            "type": "number"
        },
        {
            "name": "replacedOrderId",
            "baseName": "replaced_order_id",
            "type": "string"
        },
        {
            "name": "requestCancelTime",
            "baseName": "request_cancel_time",
            "type": "number"
        },
        {
            "name": "rtsSlaTime",
            "baseName": "rts_sla_time",
            "type": "number"
        },
        {
            "name": "rtsTime",
            "baseName": "rts_time",
            "type": "number"
        },
        {
            "name": "sellerNote",
            "baseName": "seller_note",
            "type": "string"
        },
        {
            "name": "shippingDueTime",
            "baseName": "shipping_due_time",
            "type": "number"
        },
        {
            "name": "shippingProvider",
            "baseName": "shipping_provider",
            "type": "string"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingType",
            "baseName": "shipping_type",
            "type": "string"
        },
        {
            "name": "splitOrCombineTag",
            "baseName": "split_or_combine_tag",
            "type": "string"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        },
        {
            "name": "ttsSlaTime",
            "baseName": "tts_sla_time",
            "type": "number"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        },
        {
            "name": "userId",
            "baseName": "user_id",
            "type": "string"
        },
        {
            "name": "warehouseId",
            "baseName": "warehouse_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrders.attributeTypeMap;
    }
}

