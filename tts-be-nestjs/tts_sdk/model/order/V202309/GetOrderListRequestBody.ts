/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListRequestBody {
    /**
    * Buyer user ID.
    */
    'buyerUserId'?: string;
    /**
    * Filter orders to show only those that are created on or after the specified date and time. Unix timestamp.  Note: `create_time_ge` and `create_time_le` together constitute the creation time filter condition. - If `create_time_ge` is filled but `create_time_le` is empty, `create_time_le` will default to the current time. - If `create_time_le` is filled but `create_time_ge` is empty, `create_time_ge` will default to the earliest shop time.
    */
    'createTimeGe'?: number;
    /**
    * Filter orders to show only those that are created before the specified date and time. Unix timestamp. Refer to notes in `create_time_ge` for more usage information. 
    */
    'createTimeLt'?: number;
    /**
    * Whether the buyer has initiated an order cancellation request.
    */
    'isBuyerRequestCancel'?: boolean;
    /**
    * Specific order status. Available values: - `UNPAID`: The order has been placed, but payment has not been completed. - `ON_HOLD`: The order has been accepted and is awaiting fulfillment. The buyer may still cancel without the seller’s approval. If `order_type=PRE_ORDER`, the product is still awaiting release so payment will only be authorized 1 day before the release, but the seller should start preparing for the release. (Applicable only for the US and UK market). - `AWAITING_SHIPMENT`: The order is ready to be shipped, but no items have been shipped yet. - `PARTIALLY_SHIPPING`: Some items in the order have been shipped, but not all. - `AWAITING_COLLECTION`: Shipping has been arranged, but the package is waiting to be collected by the carrier. - `IN_TRANSIT`: The package has been collected by the carrier and delivery is in progress. - `DELIVERED`: The package has been delivered to the buyer. - `COMPLETED`: The order has been completed, and no further returns or refunds are allowed. - `CANCELLED`: The order has been cancelled.
    */
    'orderStatus'?: string;
    /**
    * The delivery method. - `TIKTOK`: Shipping service provided by TikTok. The seller should obtain a shipping label from TikTok. - `SELLER`: Seller provides shipping, including through 3rd party fulfillment providers on behalf of the seller. 
    */
    'shippingType'?: string;
    /**
    * Filter orders to show only those that are updated on or after the specified date and time. Unix timestamp.  Note: `update_time_ge` and `update_time_le` together define the update time filter condition. - If `update_time_ge` is filled but `update_time_le` is empty, `update_time_le` will default to the current time. - If `update_time_le` is filled but `update_time_ge` is empty, `update_time_ge` will default to the earliest shop time.
    */
    'updateTimeGe'?: number;
    /**
    * Filter orders to show only those that are updated before the specified date and time. Unix timestamp. Refer to notes in `update_time_ge` for more usage information.
    */
    'updateTimeLt'?: number;
    /**
    * Filter orders by pickup/sales warehouse IDs. Applicable only if the multi-warehouse feature is enabled. Max count: 100
    */
    'warehouseIds'?: Array<string>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "buyerUserId",
            "baseName": "buyer_user_id",
            "type": "string"
        },
        {
            "name": "createTimeGe",
            "baseName": "create_time_ge",
            "type": "number"
        },
        {
            "name": "createTimeLt",
            "baseName": "create_time_lt",
            "type": "number"
        },
        {
            "name": "isBuyerRequestCancel",
            "baseName": "is_buyer_request_cancel",
            "type": "boolean"
        },
        {
            "name": "orderStatus",
            "baseName": "order_status",
            "type": "string"
        },
        {
            "name": "shippingType",
            "baseName": "shipping_type",
            "type": "string"
        },
        {
            "name": "updateTimeGe",
            "baseName": "update_time_ge",
            "type": "number"
        },
        {
            "name": "updateTimeLt",
            "baseName": "update_time_lt",
            "type": "number"
        },
        {
            "name": "warehouseIds",
            "baseName": "warehouse_ids",
            "type": "Array<string>"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListRequestBody.attributeTypeMap;
    }
}

