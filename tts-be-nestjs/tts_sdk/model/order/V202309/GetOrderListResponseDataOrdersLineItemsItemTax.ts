/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202309GetOrderListResponseDataOrdersLineItemsItemTax {
    /**
    * Tax amount.
    */
    'taxAmount'?: string;
    /**
    * Tax rate.
    */
    'taxRate'?: string;
    /**
    * Tax type. Available values: - `SALES_TAX` (US market sales tax)  **Note**: Currently only sales tax is available.
    */
    'taxType'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "taxAmount",
            "baseName": "tax_amount",
            "type": "string"
        },
        {
            "name": "taxRate",
            "baseName": "tax_rate",
            "type": "string"
        },
        {
            "name": "taxType",
            "baseName": "tax_type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202309GetOrderListResponseDataOrdersLineItemsItemTax.attributeTypeMap;
    }
}

