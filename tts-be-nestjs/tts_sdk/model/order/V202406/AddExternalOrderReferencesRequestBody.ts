/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesRequestBodyOrders } from './AddExternalOrderReferencesRequestBodyOrders';

export class Order202406AddExternalOrderReferencesRequestBody {
    /**
    * Max count: 100.
    */
    'orders'?: Array<Order202406AddExternalOrderReferencesRequestBodyOrders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<Order202406AddExternalOrderReferencesRequestBodyOrders>"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesRequestBody.attributeTypeMap;
    }
}

