/**
 * tik<PERSON> shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems } from './AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems';

export class Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrder {
    /**
    * The corresponding order ID in your OMS.
    */
    'id'?: string;
    /**
    * Max count: 100.
    */
    'lineItems'?: Array<Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems>;
    /**
    * The alias of your OMS. Possible values: - SHOPIFY - WOOCOMMERCE - BIGCOMMERCE - MAGENTO - SALESFORCE_COMMERCE_CLOUD - CHANNEL_ADVISOR - AMAZON - ORDER_MANAGEMENT_SYSTEM - WAREHOUSE_MANAGEMENT_SYSTEM - ERP_SYSTEM  Notes: - To attach information from multiple OMSs to the same order in TikTok Shop, call this API multiple times, each with the same TikTok Shop order and a different OMS. - To edit the attached information for a TikTok Shop order, call the API with the same OMS.
    */
    'platform'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "lineItems",
            "baseName": "line_items",
            "type": "Array<Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems>"
        },
        {
            "name": "platform",
            "baseName": "platform",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrder.attributeTypeMap;
    }
}

