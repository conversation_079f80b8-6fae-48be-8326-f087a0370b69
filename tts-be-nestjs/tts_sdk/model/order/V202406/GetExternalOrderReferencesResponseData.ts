/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406GetExternalOrderReferencesResponseDataExternalOrders } from './GetExternalOrderReferencesResponseDataExternalOrders';

export class Order202406GetExternalOrderReferencesResponseData {
    /**
    * A list of orders that have been synced between your OMS and TikTok Shop.  Note: If you\'ve synced order information between multiple OMSs to the same TikTok Shop order, you\'ll retrieve all external order information.
    */
    'externalOrders'?: Array<Order202406GetExternalOrderReferencesResponseDataExternalOrders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "externalOrders",
            "baseName": "external_orders",
            "type": "Array<Order202406GetExternalOrderReferencesResponseDataExternalOrders>"
        }    ];

    static getAttributeTypeMap() {
        return Order202406GetExternalOrderReferencesResponseData.attributeTypeMap;
    }
}

