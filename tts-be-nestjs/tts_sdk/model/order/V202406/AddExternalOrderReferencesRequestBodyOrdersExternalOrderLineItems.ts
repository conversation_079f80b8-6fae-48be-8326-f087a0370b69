/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems {
    /**
    * Line item ID in your OMS.
    */
    'id'?: string;
    /**
    * Line item ID in TikTok Shop.
    */
    'originId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "originId",
            "baseName": "origin_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrderLineItems.attributeTypeMap;
    }
}

