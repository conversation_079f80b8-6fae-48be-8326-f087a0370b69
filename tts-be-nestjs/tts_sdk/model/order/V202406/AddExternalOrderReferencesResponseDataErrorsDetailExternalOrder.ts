/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Order202406AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder {
    /**
    * The corresponding order ID in your OMS.
    */
    'id'?: string;
    /**
    * The alias of your OMS.
    */
    'platform'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "platform",
            "baseName": "platform",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder.attributeTypeMap;
    }
}

