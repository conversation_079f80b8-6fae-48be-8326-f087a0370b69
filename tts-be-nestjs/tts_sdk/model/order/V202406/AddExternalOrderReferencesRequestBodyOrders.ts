/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrder } from './AddExternalOrderReferencesRequestBodyOrdersExternalOrder';

export class Order202406AddExternalOrderReferencesRequestBodyOrders {
    'externalOrder'?: Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrder;
    /**
    * The unique identifier for a TikTok Shop order.
    */
    'id'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "externalOrder",
            "baseName": "external_order",
            "type": "Order202406AddExternalOrderReferencesRequestBodyOrdersExternalOrder"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesRequestBodyOrders.attributeTypeMap;
    }
}

