/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesResponseDataErrors } from './AddExternalOrderReferencesResponseDataErrors';

export class Order202406AddExternalOrderReferencesResponseData {
    /**
    * A list of error codes and their corresponding messages.
    */
    'errors'?: Array<Order202406AddExternalOrderReferencesResponseDataErrors>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Order202406AddExternalOrderReferencesResponseDataErrors>"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesResponseData.attributeTypeMap;
    }
}

