/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesResponseDataErrorsDetail } from './AddExternalOrderReferencesResponseDataErrorsDetail';

export class Order202406AddExternalOrderReferencesResponseDataErrors {
    /**
    * A machine-readable response code that represents the request result.
    */
    'code'?: string;
    'detail'?: Order202406AddExternalOrderReferencesResponseDataErrorsDetail;
    /**
    * A human-readable message that describes the success or failure of the API request.
    */
    'message'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "string"
        },
        {
            "name": "detail",
            "baseName": "detail",
            "type": "Order202406AddExternalOrderReferencesResponseDataErrorsDetail"
        },
        {
            "name": "message",
            "baseName": "message",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesResponseDataErrors.attributeTypeMap;
    }
}

