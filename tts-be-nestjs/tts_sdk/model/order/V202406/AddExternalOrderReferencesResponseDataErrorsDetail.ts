/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202406AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder } from './AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder';

export class Order202406AddExternalOrderReferencesResponseDataErrorsDetail {
    'externalOrder'?: Order202406AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder;
    /**
    * The unique identifier for a TikTok Shop order.
    */
    'orderId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "externalOrder",
            "baseName": "external_order",
            "type": "Order202406AddExternalOrderReferencesResponseDataErrorsDetailExternalOrder"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202406AddExternalOrderReferencesResponseDataErrorsDetail.attributeTypeMap;
    }
}

