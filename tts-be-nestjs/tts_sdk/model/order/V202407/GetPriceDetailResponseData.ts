/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Order202407GetPriceDetailResponseDataLineItems } from './GetPriceDetailResponseDataLineItems';

export class Order202407GetPriceDetailResponseData {
    /**
    * COD fee charged by shipping aggregators. For regions outside of Saudi Arabia, the value is `0.00`.
    */
    'codFee'?: string;
    /**
    * COD fee charged by shipping aggregators including tax. For regions outside of Saudi Arabia, the value is `0.00`.
    */
    'codFeeNetAmount'?: string;
    /**
    * Currency Type. Three-letter code, see [ISO 4217](https://www.iso.org/iso-4217-currency-codes.html).
    */
    'currency'?: string;
    /**
    * Each object is the same as the \"data\" field (line 5) without \"line_items\"
    */
    'lineItems'?: Array<Order202407GetPriceDetailResponseDataLineItems>;
    /**
    * Price after tax 
    */
    'netPriceAmount'?: string;
    /**
    * Payment of the order from the buyer. Calculation: `sku_sale_price` + `shipping_sale_price` + `tax_amount` + `small_order_fee`
    */
    'payment'?: string;
    /**
    * Shipping discount covered by the platfrom 
    */
    'shippingFeeDeductionPlatform'?: string;
    /**
    * \"Shipping discount covered by the platfrom voucher 1010000-PLATFROM_NEW_USER 1020000-SELLER_SKU_PRICE 1030000-PLATFORM_FREE_SHIPPING\"
    */
    'shippingFeeDeductionPlatformVoucher'?: string;
    /**
    * Shipping discount covered by the seller.
    */
    'shippingFeeDeductionSeller'?: string;
    /**
    * Original shipping price 
    */
    'shippingListPrice'?: string;
    /**
    * Promotional shipping price Calculation: shipping_list_price - shipping_fee_deduction -shipping_fee_deduction_platform
    */
    'shippingSalePrice'?: string;
    /**
    * Original sku list price of the gift product from the seller including tax.
    */
    'skuGiftNetPrice'?: string;
    /**
    * Original sku list price of the gift product from the seller.
    */
    'skuGiftOriginalPrice'?: string;
    /**
    * Total MSRP price of the products.
    */
    'skuListPrice'?: string;
    /**
    * Total promotional sale price of the products. Calculation: `sku_list_price` - `subtotal_deduction_seller` - `subtotal_deduction_platform`
    */
    'skuSalePrice'?: string;
    /**
    * Total promotional sale price of the products including tax. Calculation: `sku_sale_price` + `subtotal_tax_amount`
    */
    'subtotal'?: string;
    /**
    * Platform provided price discount on the product 
    */
    'subtotalDeductionPlatform'?: string;
    /**
    * Seller provided price discount on the product 
    */
    'subtotalDeductionSeller'?: string;
    /**
    * Total tax amount on the product 
    */
    'subtotalTaxAmount'?: string;
    /**
    * Total tax amount. Calculation: subtotal_tax_amount + shipping_fee_tax（in TaxDetail） + cod_fee_tax（TaxDetail）
    */
    'taxAmount'?: string;
    /**
    * Tax rate 
    */
    'taxRate'?: string;
    /**
    * Total number of the original price of the order. Calculation: `sku_list_price` + `shipping_list_price`
    */
    'total'?: string;
    /**
    * \"Type of the platform-providing discount on the product. Possible ennumerations: `1010000-PLATFROM_NEW_USER`, `1020000-SELLER_SKU_PRICE`, `1030000-PLATFORM_FREE_SHIPPING`\"
    */
    'voucherDeductionPlatform'?: string;
    /**
    * \"Type of the seller-providing discount on the product. Possible ennumerations: `1010000-PLATFROM_NEW_USER`, `1020000-SELLER_SKU_PRICE`, `1030000-PLATFORM_FREE_SHIPPING`\"
    */
    'voucherDeductionSeller'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "codFee",
            "baseName": "cod_fee",
            "type": "string"
        },
        {
            "name": "codFeeNetAmount",
            "baseName": "cod_fee_net_amount",
            "type": "string"
        },
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "lineItems",
            "baseName": "line_items",
            "type": "Array<Order202407GetPriceDetailResponseDataLineItems>"
        },
        {
            "name": "netPriceAmount",
            "baseName": "net_price_amount",
            "type": "string"
        },
        {
            "name": "payment",
            "baseName": "payment",
            "type": "string"
        },
        {
            "name": "shippingFeeDeductionPlatform",
            "baseName": "shipping_fee_deduction_platform",
            "type": "string"
        },
        {
            "name": "shippingFeeDeductionPlatformVoucher",
            "baseName": "shipping_fee_deduction_platform_voucher",
            "type": "string"
        },
        {
            "name": "shippingFeeDeductionSeller",
            "baseName": "shipping_fee_deduction_seller",
            "type": "string"
        },
        {
            "name": "shippingListPrice",
            "baseName": "shipping_list_price",
            "type": "string"
        },
        {
            "name": "shippingSalePrice",
            "baseName": "shipping_sale_price",
            "type": "string"
        },
        {
            "name": "skuGiftNetPrice",
            "baseName": "sku_gift_net_price",
            "type": "string"
        },
        {
            "name": "skuGiftOriginalPrice",
            "baseName": "sku_gift_original_price",
            "type": "string"
        },
        {
            "name": "skuListPrice",
            "baseName": "sku_list_price",
            "type": "string"
        },
        {
            "name": "skuSalePrice",
            "baseName": "sku_sale_price",
            "type": "string"
        },
        {
            "name": "subtotal",
            "baseName": "subtotal",
            "type": "string"
        },
        {
            "name": "subtotalDeductionPlatform",
            "baseName": "subtotal_deduction_platform",
            "type": "string"
        },
        {
            "name": "subtotalDeductionSeller",
            "baseName": "subtotal_deduction_seller",
            "type": "string"
        },
        {
            "name": "subtotalTaxAmount",
            "baseName": "subtotal_tax_amount",
            "type": "string"
        },
        {
            "name": "taxAmount",
            "baseName": "tax_amount",
            "type": "string"
        },
        {
            "name": "taxRate",
            "baseName": "tax_rate",
            "type": "string"
        },
        {
            "name": "total",
            "baseName": "total",
            "type": "string"
        },
        {
            "name": "voucherDeductionPlatform",
            "baseName": "voucher_deduction_platform",
            "type": "string"
        },
        {
            "name": "voucherDeductionSeller",
            "baseName": "voucher_deduction_seller",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Order202407GetPriceDetailResponseData.attributeTypeMap;
    }
}

