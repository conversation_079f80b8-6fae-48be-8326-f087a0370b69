/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202502UploadInvoiceResponseDataErrors } from './UploadInvoiceResponseDataErrors';

export class Fulfillment202502UploadInvoiceResponseData {
    /**
    * The list of errors that occurred.
    */
    'errors'?: Array<Fulfillment202502UploadInvoiceResponseDataErrors>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Fulfillment202502UploadInvoiceResponseDataErrors>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202502UploadInvoiceResponseData.attributeTypeMap;
    }
}

