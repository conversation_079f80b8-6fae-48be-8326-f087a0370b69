/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202502UploadInvoiceRequestBodyInvoices {
    /**
    * Base64 encoding of the invoice file to upload. Max file size: 1MB
    */
    'file'?: string;
    /**
    * The invoice file format. Possible values:  - `XML`
    */
    'fileType'?: string;
    /**
    * The list of TikTok Shop order IDs, retrieved from [Get Order List](650aa8094a0bb702c06df242).
    */
    'orderIds'?: Array<string>;
    /**
    * The TikTok Shop package ID, retrieved from [Search Package](650aa592bace3e02b75db748).
    */
    'packageId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "file",
            "baseName": "file",
            "type": "string"
        },
        {
            "name": "fileType",
            "baseName": "file_type",
            "type": "string"
        },
        {
            "name": "orderIds",
            "baseName": "order_ids",
            "type": "Array<string>"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202502UploadInvoiceRequestBodyInvoices.attributeTypeMap;
    }
}

