/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309BatchShipPackagesRequestBodyPackages } from './BatchShipPackagesRequestBodyPackages';

export class Fulfillment202309BatchShipPackagesRequestBody {
    /**
    * Input list of packages you would like to batch ship.
    */
    'packages'?: Array<Fulfillment202309BatchShipPackagesRequestBodyPackages>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "packages",
            "baseName": "packages",
            "type": "Array<Fulfillment202309BatchShipPackagesRequestBodyPackages>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309BatchShipPackagesRequestBody.attributeTypeMap;
    }
}

