/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309SearchPackageResponseDataPackagesOrdersSkus {
    /**
    * The SKU ID. 
    */
    'id'?: string;
    /**
    * The SKU image in order snapshot. 
    */
    'imageUrl'?: string;
    /**
    * The SKU name.
    */
    'name'?: string;
    /**
    * The SKU quantity.
    */
    'quantity'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "imageUrl",
            "baseName": "image_url",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "quantity",
            "baseName": "quantity",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SearchPackageResponseDataPackagesOrdersSkus.attributeTypeMap;
    }
}

