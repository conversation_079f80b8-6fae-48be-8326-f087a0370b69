/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309CreatePackagesResponseDataShippingServiceInfo {
    /**
    * Currency of the price.
    */
    'currency'?: string;
    /**
    * The minimum estimated duration required for package delivery.
    */
    'earliestDeliveryDays'?: number;
    /**
    * Shipping service ID.
    */
    'id'?: string;
    /**
    * The maximum estimated duration required for package delivery.
    */
    'latestDeliveryDays'?: number;
    /**
    * Shipping service name.
    */
    'name'?: string;
    /**
    * Estimated price for this service.
    */
    'price'?: string;
    /**
    * Shipping provider ID.
    */
    'shippingProviderId'?: string;
    /**
    * Shipping provider name.
    */
    'shippingProviderName'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "currency",
            "baseName": "currency",
            "type": "string"
        },
        {
            "name": "earliestDeliveryDays",
            "baseName": "earliest_delivery_days",
            "type": "number"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "latestDeliveryDays",
            "baseName": "latest_delivery_days",
            "type": "number"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "price",
            "baseName": "price",
            "type": "string"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CreatePackagesResponseDataShippingServiceInfo.attributeTypeMap;
    }
}

