/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309BatchShipPackagesResponseDataErrors } from './BatchShipPackagesResponseDataErrors';

export class Fulfillment202309BatchShipPackagesResponseData {
    /**
    * Return list of possible errors during package batch shipment attempt.
    */
    'errors'?: Array<Fulfillment202309BatchShipPackagesResponseDataErrors>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Fulfillment202309BatchShipPackagesResponseDataErrors>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309BatchShipPackagesResponseData.attributeTypeMap;
    }
}

