/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetPackageDetailResponseDataInsurance {
    /**
    * The insurance claim status. Available values: - `NOT_STARTED`: Claim has not been initiated for this package. - `CLAIM_PENDING`: Claim is currently under review. - `APPROVED`: Claim has been approved. - `DECLINED`: Claim has been declined.
    */
    'claimStatus'?: string;
    /**
    * The insurance coverage amount for the package. Units: USD.
    */
    'coverageAmount'?: string;
    /**
    * Whether the order is eligible for an insurance claim, based on eligible refund reasons.
    */
    'isClaimEligible'?: boolean;
    /**
    * Whether insurance has been purchased for the package.
    */
    'isPurchased'?: boolean;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "claimStatus",
            "baseName": "claim_status",
            "type": "string"
        },
        {
            "name": "coverageAmount",
            "baseName": "coverage_amount",
            "type": "string"
        },
        {
            "name": "isClaimEligible",
            "baseName": "is_claim_eligible",
            "type": "boolean"
        },
        {
            "name": "isPurchased",
            "baseName": "is_purchased",
            "type": "boolean"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageDetailResponseDataInsurance.attributeTypeMap;
    }
}

