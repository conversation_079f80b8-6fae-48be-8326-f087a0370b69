/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetPackageDetailResponseDataWeight {
    /**
    * The unit of measurement used to measure the weight. Possible values: - `GRAM` - `POUND`
    */
    'unit'?: string;
    /**
    * The value of the weight of the scheduled package.
    */
    'value'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "value",
            "baseName": "value",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageDetailResponseDataWeight.attributeTypeMap;
    }
}

