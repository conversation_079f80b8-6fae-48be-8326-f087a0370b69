/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SearchPackageResponseDataPackagesOrders } from './SearchPackageResponseDataPackagesOrders';

export class Fulfillment202309SearchPackageResponseDataPackages {
    /**
    * Package creation time. Unix timestamp.
    */
    'createTime'?: number;
    /**
    * Package ID.
    */
    'id'?: string;
    /**
    * The order line item ID contained in the package. 
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * The response list of TikTok Shop orders. 
    */
    'orders'?: Array<Fulfillment202309SearchPackageResponseDataPackagesOrders>;
    /**
    * Package shipping provider ID.
    */
    'shippingProviderId'?: string;
    /**
    * Package shipping provider.
    */
    'shippingProviderName'?: string;
    /**
    * Possible values: - `PROCESSING`: Package has been arranged by seller. Waiting for carrier to collect the parcel. - `FULFILLING`: Package has been collected by carrier and in transit. - `COMPLETED`: Package has been delivered. - `CANCELLED`: Package has been canceled. Normally, the package is canceled due to the package being lost or damaged.
    */
    'status'?: string;
    /**
    * Package tracking number.
    */
    'trackingNumber'?: string;
    /**
    * Package latest update time. Unix timestamp
    */
    'updateTime'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<Fulfillment202309SearchPackageResponseDataPackagesOrders>"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        },
        {
            "name": "status",
            "baseName": "status",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SearchPackageResponseDataPackages.attributeTypeMap;
    }
}

