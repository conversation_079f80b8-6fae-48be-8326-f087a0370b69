/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetTrackingResponseDataTracking {
    /**
    * Tracking status description.
    */
    'description'?: string;
    /**
    * Tracking status update time. Unix timestamp in milliseconds.
    */
    'updateTimeMillis'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "description",
            "baseName": "description",
            "type": "string"
        },
        {
            "name": "updateTimeMillis",
            "baseName": "update_time_millis",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetTrackingResponseDataTracking.attributeTypeMap;
    }
}

