/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetPackageDetailResponseDataOrdersSkus } from './GetPackageDetailResponseDataOrdersSkus';

export class Fulfillment202309GetPackageDetailResponseDataOrders {
    /**
    * TikTok Shop order ID.
    */
    'id'?: string;
    /**
    * SKU information.
    */
    'skus'?: Array<Fulfillment202309GetPackageDetailResponseDataOrdersSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Fulfillment202309GetPackageDetailResponseDataOrdersSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageDetailResponseDataOrders.attributeTypeMap;
    }
}

