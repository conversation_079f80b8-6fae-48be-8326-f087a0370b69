/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309CreatePackagesResponseDataWeight {
    /**
    * The unit of measurement for the package weight.  Available values: - `GRAM` - `POUND`
    */
    'unit'?: string;
    /**
    * The numerical value of the package weight.
    */
    'value'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "value",
            "baseName": "value",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CreatePackagesResponseDataWeight.attributeTypeMap;
    }
}

