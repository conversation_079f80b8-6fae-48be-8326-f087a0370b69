/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SearchCombinablePackagesResponseDataCombinablePackages } from './SearchCombinablePackagesResponseDataCombinablePackages';

export class Fulfillment202309SearchCombinablePackagesResponseData {
    /**
    * List of eligible packages that can be combined.
    */
    'combinablePackages'?: Array<Fulfillment202309SearchCombinablePackagesResponseDataCombinablePackages>;
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The number of orders that meet the query conditions.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "combinablePackages",
            "baseName": "combinable_packages",
            "type": "Array<Fulfillment202309SearchCombinablePackagesResponseDataCombinablePackages>"
        },
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SearchCombinablePackagesResponseData.attributeTypeMap;
    }
}

