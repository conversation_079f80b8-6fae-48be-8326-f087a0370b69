/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309CombinePackageResponseDataErrors } from './CombinePackageResponseDataErrors';
import { Fulfillment202309CombinePackageResponseDataPackages } from './CombinePackageResponseDataPackages';

export class Fulfillment202309CombinePackageResponseData {
    /**
    * Return list of possible errors.
    */
    'errors'?: Array<Fulfillment202309CombinePackageResponseDataErrors>;
    /**
    * Return list of successfully combined packages.
    */
    'packages'?: Array<Fulfillment202309CombinePackageResponseDataPackages>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Fulfillment202309CombinePackageResponseDataErrors>"
        },
        {
            "name": "packages",
            "baseName": "packages",
            "type": "Array<Fulfillment202309CombinePackageResponseDataPackages>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CombinePackageResponseData.attributeTypeMap;
    }
}

