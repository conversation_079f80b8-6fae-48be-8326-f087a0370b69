/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetOrderSplitAttributesResponseDataSplitAttributes } from './GetOrderSplitAttributesResponseDataSplitAttributes';

export class Fulfillment202309GetOrderSplitAttributesResponseData {
    /**
    * Specific return information (can return multiple TikTok Shop order IDs).
    */
    'splitAttributes'?: Array<Fulfillment202309GetOrderSplitAttributesResponseDataSplitAttributes>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "splitAttributes",
            "baseName": "split_attributes",
            "type": "Array<Fulfillment202309GetOrderSplitAttributesResponseDataSplitAttributes>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetOrderSplitAttributesResponseData.attributeTypeMap;
    }
}

