/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309BatchShipPackagesRequestBodyPackagesPickupSlot } from './BatchShipPackagesRequestBodyPackagesPickupSlot';
import { Fulfillment202309BatchShipPackagesRequestBodyPackagesSelfShipment } from './BatchShipPackagesRequestBodyPackagesSelfShipment';

export class Fulfillment202309BatchShipPackagesRequestBodyPackages {
    /**
    * Possible values: - `PICKUP`: A shipping provider will pickup the package(s) from the seller\'s pickup address. - `DROP_OFF`: The seller will need to drop off the package(s) at a designated location.
    */
    'handoverMethod'?: string;
    /**
    * Package ID.
    */
    'id'?: string;
    'pickupSlot'?: Fulfillment202309BatchShipPackagesRequestBodyPackagesPickupSlot;
    'selfShipment'?: Fulfillment202309BatchShipPackagesRequestBodyPackagesSelfShipment;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "pickupSlot",
            "baseName": "pickup_slot",
            "type": "Fulfillment202309BatchShipPackagesRequestBodyPackagesPickupSlot"
        },
        {
            "name": "selfShipment",
            "baseName": "self_shipment",
            "type": "Fulfillment202309BatchShipPackagesRequestBodyPackagesSelfShipment"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309BatchShipPackagesRequestBodyPackages.attributeTypeMap;
    }
}

