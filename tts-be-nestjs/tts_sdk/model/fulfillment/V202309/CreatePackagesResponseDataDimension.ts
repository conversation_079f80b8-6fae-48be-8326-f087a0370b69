/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309CreatePackagesResponseDataDimension {
    /**
    * Package height.
    */
    'height'?: string;
    /**
    * Package length.
    */
    'length'?: string;
    /**
    * The unit of measurement for the package dimensions.  Available values: - `CM` - `INCH`
    */
    'unit'?: string;
    /**
    * Package width.
    */
    'width'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "string"
        },
        {
            "name": "length",
            "baseName": "length",
            "type": "string"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CreatePackagesResponseDataDimension.attributeTypeMap;
    }
}

