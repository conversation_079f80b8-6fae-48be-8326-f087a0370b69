/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309CreatePackagesRequestBodyDimension } from './CreatePackagesRequestBodyDimension';
import { Fulfillment202309CreatePackagesRequestBodyWeight } from './CreatePackagesRequestBodyWeight';

export class Fulfillment202309CreatePackagesRequestBody {
    'dimension'?: Fulfillment202309CreatePackagesRequestBodyDimension;
    /**
    * TikTok Shop order ID.
    */
    'orderId'?: string;
    /**
    * List of order line item IDs.
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * Specify the shipping service used.  If not specified, use the default service obtained from [Get Eligible Shipping Service](https://partner.tiktokshop.com/docv2/page/650aa6b2bace3e02b75dda4e).
    */
    'shippingServiceId'?: string;
    'weight'?: Fulfillment202309CreatePackagesRequestBodyWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309CreatePackagesRequestBodyDimension"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "shippingServiceId",
            "baseName": "shipping_service_id",
            "type": "string"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309CreatePackagesRequestBodyWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CreatePackagesRequestBody.attributeTypeMap;
    }
}

