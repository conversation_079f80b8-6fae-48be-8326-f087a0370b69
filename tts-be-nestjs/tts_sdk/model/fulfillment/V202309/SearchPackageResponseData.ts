/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SearchPackageResponseDataPackages } from './SearchPackageResponseDataPackages';

export class Fulfillment202309SearchPackageResponseData {
    /**
    * An opaque token used to retrieve the next page of a paginated result set. Provide this value in the `page_token` parameter of your request if the current response does not return all the results.
    */
    'nextPageToken'?: string;
    /**
    * The response list of packages.
    */
    'packages'?: Array<Fulfillment202309SearchPackageResponseDataPackages>;
    /**
    * The number of packages that meet the query conditions.
    */
    'totalCount'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "nextPageToken",
            "baseName": "next_page_token",
            "type": "string"
        },
        {
            "name": "packages",
            "baseName": "packages",
            "type": "Array<Fulfillment202309SearchPackageResponseDataPackages>"
        },
        {
            "name": "totalCount",
            "baseName": "total_count",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SearchPackageResponseData.attributeTypeMap;
    }
}

