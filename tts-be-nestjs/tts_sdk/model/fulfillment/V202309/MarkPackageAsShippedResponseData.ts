/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309MarkPackageAsShippedResponseDataWarning } from './MarkPackageAsShippedResponseDataWarning';

export class Fulfillment202309MarkPackageAsShippedResponseData {
    /**
    * TikTok Shop order ID.
    */
    'orderId'?: string;
    /**
    * List of order line item IDs.
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * Package ID.
    */
    'packageId'?: string;
    'warning'?: Fulfillment202309MarkPackageAsShippedResponseDataWarning;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "warning",
            "baseName": "warning",
            "type": "Fulfillment202309MarkPackageAsShippedResponseDataWarning"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309MarkPackageAsShippedResponseData.attributeTypeMap;
    }
}

