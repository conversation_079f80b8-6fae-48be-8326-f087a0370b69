/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SplitOrdersRequestBodySplittableGroups } from './SplitOrdersRequestBodySplittableGroups';

export class Fulfillment202309SplitOrdersRequestBody {
    /**
    * Input list of splittable groups.
    */
    'splittableGroups'?: Array<Fulfillment202309SplitOrdersRequestBodySplittableGroups>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "splittableGroups",
            "baseName": "splittable_groups",
            "type": "Array<Fulfillment202309SplitOrdersRequestBodySplittableGroups>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SplitOrdersRequestBody.attributeTypeMap;
    }
}

