/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetEligibleShippingServiceRequestBodyDimension } from './GetEligibleShippingServiceRequestBodyDimension';
import { Fulfillment202309GetEligibleShippingServiceRequestBodyWeight } from './GetEligibleShippingServiceRequestBodyWeight';

export class Fulfillment202309GetEligibleShippingServiceRequestBody {
    'dimension'?: Fulfillment202309GetEligibleShippingServiceRequestBodyDimension;
    /**
    * Order line item ID list
    */
    'orderLineItemIds'?: Array<string>;
    'weight'?: Fulfillment202309GetEligibleShippingServiceRequestBodyWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309GetEligibleShippingServiceRequestBodyDimension"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309GetEligibleShippingServiceRequestBodyWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetEligibleShippingServiceRequestBody.attributeTypeMap;
    }
}

