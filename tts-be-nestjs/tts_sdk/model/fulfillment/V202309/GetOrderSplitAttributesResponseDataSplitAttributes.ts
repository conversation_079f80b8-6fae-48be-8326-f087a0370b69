/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetOrderSplitAttributesResponseDataSplitAttributes {
    /**
    * Whether an order can be split: - `true`: The order can be split into multiple packages. - `false`: The order cannot be split into multiple packages.
    */
    'canSplit'?: boolean;
    /**
    * TikTok Shop order ID.
    */
    'orderId'?: string;
    /**
    * The reason why the order cannot be split. Only return this field when `can_split = false`.
    */
    'reason'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "canSplit",
            "baseName": "can_split",
            "type": "boolean"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "reason",
            "baseName": "reason",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetOrderSplitAttributesResponseDataSplitAttributes.attributeTypeMap;
    }
}

