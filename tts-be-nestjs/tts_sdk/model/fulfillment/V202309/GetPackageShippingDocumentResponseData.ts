/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetPackageShippingDocumentResponseData {
    /**
    * The URL of the shipping label and packing slip generated for the specified package.  The URL is valid for 24 hours.
    */
    'docUrl'?: string;
    /**
    * The package tracking number from the shipping carrier. 
    */
    'trackingNumber'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "docUrl",
            "baseName": "doc_url",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageShippingDocumentResponseData.attributeTypeMap;
    }
}

