/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SchedulePackageHandoverResponseDataDimension } from './SchedulePackageHandoverResponseDataDimension';
import { Fulfillment202309SchedulePackageHandoverResponseDataWeight } from './SchedulePackageHandoverResponseDataWeight';

export class Fulfillment202309SchedulePackageHandoverResponseData {
    /**
    * Unix timestamp
    */
    'createTime'?: number;
    'dimension'?: Fulfillment202309SchedulePackageHandoverResponseDataDimension;
    /**
    * Schedule the package as a pickup or drop off. - PICKUP (A shipping provider will pickup the package(s) from the seller\'s pickup address)   - DROP_OFF (Seller will need to drop off the package(s) to a designated location)
    */
    'handoverMethod'?: string;
    /**
    * TikTok Shop order ID
    */
    'orderId'?: string;
    /**
    * Order line item IDs that belong to the package.
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * Package ID. 
    */
    'packageId'?: string;
    /**
    * Package shipping provider id
    */
    'shippingProviderId'?: string;
    /**
    * Package shipping provider
    */
    'shippingProviderName'?: string;
    /**
    * Package tracking number
    */
    'trackingNumber'?: string;
    /**
    * Unix timestamp
    */
    'updateTime'?: number;
    'weight'?: Fulfillment202309SchedulePackageHandoverResponseDataWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309SchedulePackageHandoverResponseDataDimension"
        },
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309SchedulePackageHandoverResponseDataWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SchedulePackageHandoverResponseData.attributeTypeMap;
    }
}

