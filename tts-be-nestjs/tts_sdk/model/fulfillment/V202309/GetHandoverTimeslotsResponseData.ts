/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetHandoverTimeslotsResponseDataPickupSlots } from './GetHandoverTimeslotsResponseDataPickupSlots';

export class Fulfillment202309GetHandoverTimeslotsResponseData {
    /**
    * Does this package support point delivery
    */
    'canDropOff'?: boolean;
    /**
    * Does this package support door-to-door collection
    */
    'canPickup'?: boolean;
    /**
    * Only for UK. Use this field to determine wheather van collection is available
    */
    'canVanCollection'?: boolean;
    /**
    * View deliverable logistics outlets via URL
    */
    'dropOffPointUrl'?: string;
    /**
    * Package pickup time slots for door-to-door collection
    */
    'pickupSlots'?: Array<Fulfillment202309GetHandoverTimeslotsResponseDataPickupSlots>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "canDropOff",
            "baseName": "can_drop_off",
            "type": "boolean"
        },
        {
            "name": "canPickup",
            "baseName": "can_pickup",
            "type": "boolean"
        },
        {
            "name": "canVanCollection",
            "baseName": "can_van_collection",
            "type": "boolean"
        },
        {
            "name": "dropOffPointUrl",
            "baseName": "drop_off_point_url",
            "type": "string"
        },
        {
            "name": "pickupSlots",
            "baseName": "pickup_slots",
            "type": "Array<Fulfillment202309GetHandoverTimeslotsResponseDataPickupSlots>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetHandoverTimeslotsResponseData.attributeTypeMap;
    }
}

