/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SearchPackageResponseDataPackagesOrdersSkus } from './SearchPackageResponseDataPackagesOrdersSkus';

export class Fulfillment202309SearchPackageResponseDataPackagesOrders {
    /**
    * TikTok Shop order ID.
    */
    'id'?: string;
    /**
    * The response list of SKUs.
    */
    'skus'?: Array<Fulfillment202309SearchPackageResponseDataPackagesOrdersSkus>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "skus",
            "baseName": "skus",
            "type": "Array<Fulfillment202309SearchPackageResponseDataPackagesOrdersSkus>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SearchPackageResponseDataPackagesOrders.attributeTypeMap;
    }
}

