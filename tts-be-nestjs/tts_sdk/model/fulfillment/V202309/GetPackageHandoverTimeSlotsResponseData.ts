/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetPackageHandoverTimeSlotsResponseDataPickupSlots } from './GetPackageHandoverTimeSlotsResponseDataPickupSlots';

export class Fulfillment202309GetPackageHandoverTimeSlotsResponseData {
    /**
    * Whether this package be dropped off at a drop-off location
    */
    'canDropOff'?: boolean;
    /**
    * Whether this package supports door-to-door collection.
    */
    'canPickup'?: boolean;
    /**
    * Specific to UK. Use this field to determine whether van collection is available.
    */
    'canVanCollection'?: boolean;
    /**
    * View package drop-off locations  via provided URL.
    */
    'dropOffPointUrl'?: string;
    /**
    * Time slot for door-to-door collection.
    */
    'pickupSlots'?: Array<Fulfillment202309GetPackageHandoverTimeSlotsResponseDataPickupSlots>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "canDropOff",
            "baseName": "can_drop_off",
            "type": "boolean"
        },
        {
            "name": "canPickup",
            "baseName": "can_pickup",
            "type": "boolean"
        },
        {
            "name": "canVanCollection",
            "baseName": "can_van_collection",
            "type": "boolean"
        },
        {
            "name": "dropOffPointUrl",
            "baseName": "drop_off_point_url",
            "type": "string"
        },
        {
            "name": "pickupSlots",
            "baseName": "pickup_slots",
            "type": "Array<Fulfillment202309GetPackageHandoverTimeSlotsResponseDataPickupSlots>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageHandoverTimeSlotsResponseData.attributeTypeMap;
    }
}

