/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309FulfillmentUploadDeliveryImageResponseData {
    /**
    * The image height returned from uploading the image. This height refers to the processed image height, not the original image height. Units: pixels.
    */
    'height'?: number;
    /**
    * The URL returned from uploading the image that can be directly opened in a browser. 
    */
    'url'?: string;
    /**
    * The image width returned from uploading the image. This width refers to the processed image width, not the original image width.  Units: pixels.
    */
    'width'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "number"
        },
        {
            "name": "url",
            "baseName": "url",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309FulfillmentUploadDeliveryImageResponseData.attributeTypeMap;
    }
}

