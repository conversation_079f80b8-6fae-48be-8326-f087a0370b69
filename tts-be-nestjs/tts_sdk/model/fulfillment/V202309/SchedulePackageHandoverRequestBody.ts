/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309SchedulePackageHandoverRequestBodyPickupSlot } from './SchedulePackageHandoverRequestBodyPickupSlot';

export class Fulfillment202309SchedulePackageHandoverRequestBody {
    /**
    * Schedule the package as a pickup or drop off. - PICKUP (A shipping provider will pickup the package(s) from the seller\'s pickup address)   - DROP_OFF (Seller will need to drop off the package(s) to a designated location)
    */
    'handoverMethod'?: string;
    /**
    * TikTok Shop order ID
    */
    'orderId'?: string;
    /**
    * Line item ID list
    */
    'orderLineItemIds'?: Array<string>;
    'pickupSlot'?: Fulfillment202309SchedulePackageHandoverRequestBodyPickupSlot;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "pickupSlot",
            "baseName": "pickup_slot",
            "type": "Fulfillment202309SchedulePackageHandoverRequestBodyPickupSlot"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309SchedulePackageHandoverRequestBody.attributeTypeMap;
    }
}

