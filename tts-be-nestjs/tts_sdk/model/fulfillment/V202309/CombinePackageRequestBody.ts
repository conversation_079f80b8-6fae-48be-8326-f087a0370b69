/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309CombinePackageRequestBodyCombinablePackages } from './CombinePackageRequestBodyCombinablePackages';

export class Fulfillment202309CombinePackageRequestBody {
    /**
    * Input list of combinable packages.
    */
    'combinablePackages'?: Array<Fulfillment202309CombinePackageRequestBodyCombinablePackages>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "combinablePackages",
            "baseName": "combinable_packages",
            "type": "Array<Fulfillment202309CombinePackageRequestBodyCombinablePackages>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CombinePackageRequestBody.attributeTypeMap;
    }
}

