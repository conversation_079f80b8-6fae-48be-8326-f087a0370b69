/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetPackageDetailResponseDataDimension } from './GetPackageDetailResponseDataDimension';
import { Fulfillment202309GetPackageDetailResponseDataInsurance } from './GetPackageDetailResponseDataInsurance';
import { Fulfillment202309GetPackageDetailResponseDataOrders } from './GetPackageDetailResponseDataOrders';
import { Fulfillment202309GetPackageDetailResponseDataPickupSlot } from './GetPackageDetailResponseDataPickupSlot';
import { Fulfillment202309GetPackageDetailResponseDataRecipientAddress } from './GetPackageDetailResponseDataRecipientAddress';
import { Fulfillment202309GetPackageDetailResponseDataSenderAddress } from './GetPackageDetailResponseDataSenderAddress';
import { Fulfillment202309GetPackageDetailResponseDataWeight } from './GetPackageDetailResponseDataWeight';

export class Fulfillment202309GetPackageDetailResponseData {
    /**
    * Package creation time. Unix timestamp.
    */
    'createTime'?: number;
    /**
    * Order delivery option ID. Delivery option ID is mapped to seller configured logistics templates ID.
    */
    'deliveryOptionId'?: string;
    /**
    * Delivery option name. For display purposes only.
    */
    'deliveryOptionName'?: string;
    'dimension'?: Fulfillment202309GetPackageDetailResponseDataDimension;
    /**
    * Whether the package is delivered by pick up or drop off. Possible values: - `PICKUP`: A Logistics carrier will pickup the package(s) from the seller\'s pickup address. - `DROP_OFF`: Seller will need to drop off the package(s) to a designated location.
    */
    'handoverMethod'?: string;
    /**
    * Whether there are multiple SKU IDs in a package.
    */
    'hasMultiSkus'?: boolean;
    'insurance'?: Fulfillment202309GetPackageDetailResponseDataInsurance;
    /**
    * For cross-border order only. Cross-border order last mile tracking number. 
    */
    'lastMileTrackingNumber'?: string;
    /**
    * Possible values: - `BUYER_UNNOTED`: The order has not been noted by buyer. - `BUYER_NOTED`: The order has been noted by buyer. 
    */
    'noteTag'?: string;
    /**
    * The order line item ID contained in the package.
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * The response list of TikTok Shop orders.
    */
    'orders'?: Array<Fulfillment202309GetPackageDetailResponseDataOrders>;
    /**
    * TikTok Shop package ID. 
    */
    'packageId'?: string;
    /**
    * Possible values: - `PROCESSING`: Package has been arranged by seller. Waiting for carrier to collect the parcel. - `FULFILLING`: Package has been collected by carrier and in transit. - `COMPLETED`: Package has been delivered. - `CANCELLED`: Package has been canceled. Normally, the package is canceled due to the package being lost or damaged.
    */
    'packageStatus'?: string;
    'pickupSlot'?: Fulfillment202309GetPackageDetailResponseDataPickupSlot;
    'recipientAddress'?: Fulfillment202309GetPackageDetailResponseDataRecipientAddress;
    'senderAddress'?: Fulfillment202309GetPackageDetailResponseDataSenderAddress;
    /**
    * Package shipping provider ID.
    */
    'shippingProviderId'?: string;
    /**
    * Package shipping provider name.
    */
    'shippingProviderName'?: string;
    /**
    * The method of delivery.  Possible values: - `TIKTOK`: Shipping service provided by TikTok. The seller should obtain a shipping label from TikTok. - `SELLER`: Seller provides shipping, including through 3rd party fulfillment providers on behalf of the seller. 
    */
    'shippingType'?: string;
    /**
    * Possible values: - `DEFAULT`: The package has not undergone any combine or split operation. - `COMBINE`: The package has been consolidated with another order. - `SPLIT`: The order has been split into multiple orders.
    */
    'splitAndCombineTag'?: string;
    /**
    * Package tracking number.
    */
    'trackingNumber'?: string;
    /**
    * The time the package has been updated. Unix timestamp.
    */
    'updateTime'?: number;
    'weight'?: Fulfillment202309GetPackageDetailResponseDataWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "deliveryOptionId",
            "baseName": "delivery_option_id",
            "type": "string"
        },
        {
            "name": "deliveryOptionName",
            "baseName": "delivery_option_name",
            "type": "string"
        },
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309GetPackageDetailResponseDataDimension"
        },
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "hasMultiSkus",
            "baseName": "has_multi_skus",
            "type": "boolean"
        },
        {
            "name": "insurance",
            "baseName": "insurance",
            "type": "Fulfillment202309GetPackageDetailResponseDataInsurance"
        },
        {
            "name": "lastMileTrackingNumber",
            "baseName": "last_mile_tracking_number",
            "type": "string"
        },
        {
            "name": "noteTag",
            "baseName": "note_tag",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "orders",
            "baseName": "orders",
            "type": "Array<Fulfillment202309GetPackageDetailResponseDataOrders>"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "packageStatus",
            "baseName": "package_status",
            "type": "string"
        },
        {
            "name": "pickupSlot",
            "baseName": "pickup_slot",
            "type": "Fulfillment202309GetPackageDetailResponseDataPickupSlot"
        },
        {
            "name": "recipientAddress",
            "baseName": "recipient_address",
            "type": "Fulfillment202309GetPackageDetailResponseDataRecipientAddress"
        },
        {
            "name": "senderAddress",
            "baseName": "sender_address",
            "type": "Fulfillment202309GetPackageDetailResponseDataSenderAddress"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "shippingProviderName",
            "baseName": "shipping_provider_name",
            "type": "string"
        },
        {
            "name": "shippingType",
            "baseName": "shipping_type",
            "type": "string"
        },
        {
            "name": "splitAndCombineTag",
            "baseName": "split_and_combine_tag",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        },
        {
            "name": "updateTime",
            "baseName": "update_time",
            "type": "number"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309GetPackageDetailResponseDataWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageDetailResponseData.attributeTypeMap;
    }
}

