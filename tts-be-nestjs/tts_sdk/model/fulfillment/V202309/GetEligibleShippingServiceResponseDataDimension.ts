/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetEligibleShippingServiceResponseDataDimension {
    /**
    * The height of the package. The length, width, height must be passed together.
    */
    'height'?: string;
    /**
    * The length of the package. The length, width, height must be passed together.
    */
    'length'?: string;
    /**
    * The unit of measurement is used to measure the length. - CM - INCH
    */
    'unit'?: string;
    /**
    * The width of the package. The length, width, height must be passed together.
    */
    'width'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "string"
        },
        {
            "name": "length",
            "baseName": "length",
            "type": "string"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetEligibleShippingServiceResponseDataDimension.attributeTypeMap;
    }
}

