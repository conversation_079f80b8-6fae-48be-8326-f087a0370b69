/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309ShipPackageRequestBodyPickupSlot } from './ShipPackageRequestBodyPickupSlot';
import { Fulfillment202309ShipPackageRequestBodySelfShipment } from './ShipPackageRequestBodySelfShipment';

export class Fulfillment202309ShipPackageRequestBody {
    /**
    * Possible values: - `PICKUP`: A logistics carrier will pick up the package(s) from the seller\'s pickup address. - `DROP_OFF`: The seller will need to drop off the package(s) to a designated location.
    */
    'handoverMethod'?: string;
    'pickupSlot'?: Fulfillment202309ShipPackageRequestBodyPickupSlot;
    'selfShipment'?: Fulfillment202309ShipPackageRequestBodySelfShipment;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "pickupSlot",
            "baseName": "pickup_slot",
            "type": "Fulfillment202309ShipPackageRequestBodyPickupSlot"
        },
        {
            "name": "selfShipment",
            "baseName": "self_shipment",
            "type": "Fulfillment202309ShipPackageRequestBodySelfShipment"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309ShipPackageRequestBody.attributeTypeMap;
    }
}

