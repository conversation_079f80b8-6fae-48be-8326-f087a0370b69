/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetEligibleShippingServiceResponseDataDimension } from './GetEligibleShippingServiceResponseDataDimension';
import { Fulfillment202309GetEligibleShippingServiceResponseDataShippingServices } from './GetEligibleShippingServiceResponseDataShippingServices';
import { Fulfillment202309GetEligibleShippingServiceResponseDataWeight } from './GetEligibleShippingServiceResponseDataWeight';

export class Fulfillment202309GetEligibleShippingServiceResponseData {
    'dimension'?: Fulfillment202309GetEligibleShippingServiceResponseDataDimension;
    /**
    * TikTok Shop order ID
    */
    'orderId'?: string;
    /**
    * Line item ID list
    */
    'orderLineId'?: Array<string>;
    /**
    * Shipping services info.
    */
    'shippingServices'?: Array<Fulfillment202309GetEligibleShippingServiceResponseDataShippingServices>;
    'weight'?: Fulfillment202309GetEligibleShippingServiceResponseDataWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309GetEligibleShippingServiceResponseDataDimension"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineId",
            "baseName": "order_line_id",
            "type": "Array<string>"
        },
        {
            "name": "shippingServices",
            "baseName": "shipping_services",
            "type": "Array<Fulfillment202309GetEligibleShippingServiceResponseDataShippingServices>"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309GetEligibleShippingServiceResponseDataWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetEligibleShippingServiceResponseData.attributeTypeMap;
    }
}

