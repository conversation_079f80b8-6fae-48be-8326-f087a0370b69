/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202309GetPackageDetailResponseDataRecipientAddress {
    /**
    * Full buyer detail address.
    */
    'addressDetail'?: string;
    /**
    * The first line of the street address
    */
    'addressLine1'?: string;
    /**
    * The second line of the street address.
    */
    'addressLine2'?: string;
    /**
    * The third line of the street address. Usually only for the Brazilian market.
    */
    'addressLine3'?: string;
    /**
    * The fourth line of the street address. Usually only for the Brazilian market.
    */
    'addressLine4'?: string;
    /**
    * The complete recipient addresses information. 
    */
    'fullAddress'?: string;
    /**
    * The name of the recipient. Please note, if this order uses platform logistics, recipient name will be desensitized
    */
    'name'?: string;
    /**
    * The telephone number of the buyer. Please note, if this order use platform logistics, phone number will be desensitized.
    */
    'phoneNumber'?: string;
    /**
    * The postal code that can be used by seller for shipping (in the U.S, this refers to the ZIP code).
    */
    'postalCode'?: string;
    /**
    * Region code.
    */
    'regionCode'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressDetail",
            "baseName": "address_detail",
            "type": "string"
        },
        {
            "name": "addressLine1",
            "baseName": "address_line1",
            "type": "string"
        },
        {
            "name": "addressLine2",
            "baseName": "address_line2",
            "type": "string"
        },
        {
            "name": "addressLine3",
            "baseName": "address_line3",
            "type": "string"
        },
        {
            "name": "addressLine4",
            "baseName": "address_line4",
            "type": "string"
        },
        {
            "name": "fullAddress",
            "baseName": "full_address",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "phoneNumber",
            "baseName": "phone_number",
            "type": "string"
        },
        {
            "name": "postalCode",
            "baseName": "postal_code",
            "type": "string"
        },
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetPackageDetailResponseDataRecipientAddress.attributeTypeMap;
    }
}

