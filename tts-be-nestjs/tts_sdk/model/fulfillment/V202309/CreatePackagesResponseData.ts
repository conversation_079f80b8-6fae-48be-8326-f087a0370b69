/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309CreatePackagesResponseDataDimension } from './CreatePackagesResponseDataDimension';
import { Fulfillment202309CreatePackagesResponseDataShippingServiceInfo } from './CreatePackagesResponseDataShippingServiceInfo';
import { Fulfillment202309CreatePackagesResponseDataWeight } from './CreatePackagesResponseDataWeight';

export class Fulfillment202309CreatePackagesResponseData {
    /**
    * The time when the product was created. Unix timestamp.
    */
    'createTime'?: number;
    'dimension'?: Fulfillment202309CreatePackagesResponseDataDimension;
    /**
    * TikTok Shop order ID.  
    */
    'orderId'?: string;
    /**
    * List of order line item IDs.
    */
    'orderLineItemIds'?: Array<string>;
    /**
    * Package ID.
    */
    'packageId'?: string;
    'shippingServiceInfo'?: Fulfillment202309CreatePackagesResponseDataShippingServiceInfo;
    'weight'?: Fulfillment202309CreatePackagesResponseDataWeight;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "createTime",
            "baseName": "create_time",
            "type": "number"
        },
        {
            "name": "dimension",
            "baseName": "dimension",
            "type": "Fulfillment202309CreatePackagesResponseDataDimension"
        },
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        },
        {
            "name": "orderLineItemIds",
            "baseName": "order_line_item_ids",
            "type": "Array<string>"
        },
        {
            "name": "packageId",
            "baseName": "package_id",
            "type": "string"
        },
        {
            "name": "shippingServiceInfo",
            "baseName": "shipping_service_info",
            "type": "Fulfillment202309CreatePackagesResponseDataShippingServiceInfo"
        },
        {
            "name": "weight",
            "baseName": "weight",
            "type": "Fulfillment202309CreatePackagesResponseDataWeight"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309CreatePackagesResponseData.attributeTypeMap;
    }
}

