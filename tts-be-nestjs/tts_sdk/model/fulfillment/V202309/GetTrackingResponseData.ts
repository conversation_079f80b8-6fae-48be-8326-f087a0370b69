/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202309GetTrackingResponseDataTracking } from './GetTrackingResponseDataTracking';

export class Fulfillment202309GetTrackingResponseData {
    /**
    * The return list of tracking information.
    */
    'tracking'?: Array<Fulfillment202309GetTrackingResponseDataTracking>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "tracking",
            "baseName": "tracking",
            "type": "Array<Fulfillment202309GetTrackingResponseDataTracking>"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202309GetTrackingResponseData.attributeTypeMap;
    }
}

