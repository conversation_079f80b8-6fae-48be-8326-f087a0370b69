/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202407CreateFirstMileBundleResponseDataErrorsDetail } from './CreateFirstMileBundleResponseDataErrorsDetail';

export class Fulfillment202407CreateFirstMileBundleResponseDataErrors {
    /**
    * The success or failure status code returned in API response.
    */
    'code'?: number;
    'detail'?: Fulfillment202407CreateFirstMileBundleResponseDataErrorsDetail;
    /**
    * The success or failure messages returned in API response. Reasons of failure will be described in the message.
    */
    'message'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "code",
            "baseName": "code",
            "type": "number"
        },
        {
            "name": "detail",
            "baseName": "detail",
            "type": "Fulfillment202407CreateFirstMileBundleResponseDataErrorsDetail"
        },
        {
            "name": "message",
            "baseName": "message",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202407CreateFirstMileBundleResponseDataErrors.attributeTypeMap;
    }
}

