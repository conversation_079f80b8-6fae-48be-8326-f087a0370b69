/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202407CreateFirstMileBundleRequestBody {
    /**
    * The way you send the first-mile bundle. Possible enumerations are: - `PICKUP`: You use the logistic service provided by TikTok Shop to send the bundle. - `DROP_OFF`: You contact the logistic provider and send the bundle. The logistic provider must be registered at TikTok Shop.
    */
    'handoverMethod'?: string;
    /**
    * The IDs of all the orders sent in a single first-mile bundle. The orders must follow the restrictions: - Each of the orders must exist and be RTS and shipping label printed. - The orders are sent by the same seller. - The orders belong to a single group of TikTok Shop service districts. The groups are:   - Group 1: PH, SG, MY, VN, TH, and JP.   - Group 2: DE, FR, IT, ES.  You can not create first mile bundles for US/UK orders using this API.
    */
    'orderIds'?: Array<string>;
    /**
    * Last 4 digits of the sender\'s phone number. Required when `handover_method == DROP_OFF`.
    */
    'phoneTailNumber'?: string;
    /**
    * The logistic provider ID in TikTok Shop. Required when `handover_method == DROP_OFF`.
    */
    'shippingProviderId'?: string;
    /**
    * The logistic tracking number of the bundle. Required when `handover_method == DROP_OFF`.
    */
    'trackingNumber'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "handoverMethod",
            "baseName": "handover_method",
            "type": "string"
        },
        {
            "name": "orderIds",
            "baseName": "order_ids",
            "type": "Array<string>"
        },
        {
            "name": "phoneTailNumber",
            "baseName": "phone_tail_number",
            "type": "string"
        },
        {
            "name": "shippingProviderId",
            "baseName": "shipping_provider_id",
            "type": "string"
        },
        {
            "name": "trackingNumber",
            "baseName": "tracking_number",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202407CreateFirstMileBundleRequestBody.attributeTypeMap;
    }
}

