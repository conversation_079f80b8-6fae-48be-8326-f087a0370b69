/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Fulfillment202407CreateFirstMileBundleResponseDataErrorsDetail {
    /**
    * TikTok Shop order ID
    */
    'orderId'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "orderId",
            "baseName": "order_id",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202407CreateFirstMileBundleResponseDataErrorsDetail.attributeTypeMap;
    }
}

