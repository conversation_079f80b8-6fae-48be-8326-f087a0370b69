/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Fulfillment202407CreateFirstMileBundleResponseDataErrors } from './CreateFirstMileBundleResponseDataErrors';

export class Fulfillment202407CreateFirstMileBundleResponseData {
    /**
    * Specific return information (returns multiple errors and reasons)
    */
    'errors'?: Array<Fulfillment202407CreateFirstMileBundleResponseDataErrors>;
    /**
    * The ID of the first-mile bundle.
    */
    'firstMileBundleId'?: string;
    /**
    * The returned waybill link.
    */
    'url'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "errors",
            "baseName": "errors",
            "type": "Array<Fulfillment202407CreateFirstMileBundleResponseDataErrors>"
        },
        {
            "name": "firstMileBundleId",
            "baseName": "first_mile_bundle_id",
            "type": "string"
        },
        {
            "name": "url",
            "baseName": "url",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Fulfillment202407CreateFirstMileBundleResponseData.attributeTypeMap;
    }
}

