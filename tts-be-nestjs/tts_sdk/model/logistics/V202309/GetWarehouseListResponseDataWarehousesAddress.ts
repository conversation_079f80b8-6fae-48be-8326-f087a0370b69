/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetWarehouseListResponseDataWarehousesAddressGeolocation } from './GetWarehouseListResponseDataWarehousesAddressGeolocation';

export class Logistics202309GetWarehouseListResponseDataWarehousesAddress {
    /**
    * The first line of the warehouse address, like street name and street number. Note: For Brazilian market, this represents the neighborhood or district.
    */
    'addressLine1'?: string;
    /**
    * The second line of the warehouse address, like flat, apartment, or suit. Note: For Brazilian market, this represents the street name.
    */
    'addressLine2'?: string;
    /**
    * This represents the street number. If it\'s `s/n`, it means null. Note: Available only in Brazilian market.
    */
    'addressLine3'?: string;
    /**
    * This represents supplement information, like flat, apartment, or suit (optional). Note: Available only in Brazilian market.
    */
    'addressLine4'?: string;
    /**
    * Warehouse city.
    */
    'city'?: string;
    /**
    * Warehouse contact person name.
    */
    'contactPerson'?: string;
    /**
    * Warehouse district.
    */
    'distict'?: string;
    /**
    * Kanji first name Applicable only for the JP market.
    */
    'firstName'?: string;
    /**
    * Hiragana or Katakana first name Applicable only for the JP market.
    */
    'firstNameLocalScript'?: string;
    /**
    * The combined warehouse address, including the street address and other address information such as apartment number, building, floor..etc (optional)
    */
    'fullAddress'?: string;
    'geolocation'?: Logistics202309GetWarehouseListResponseDataWarehousesAddressGeolocation;
    /**
    * Kanji last name Applicable only for the JP market.
    */
    'lastName'?: string;
    /**
    * Hiragana or Katakana last name Applicable only for the JP market.
    */
    'lastNameLocalScript'?: string;
    /**
    * Warehouse phone number.
    */
    'phoneNumber'?: string;
    /**
    * Warehouse address postal code (also known as zip code)
    */
    'postalCode'?: string;
    /**
    * Warehouse region.
    */
    'region'?: string;
    /**
    * Warehouse region code.
    */
    'regionCode'?: string;
    /**
    * Warehouse state or province.
    */
    'state'?: string;
    /**
    * Warehouse town.
    */
    'town'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "addressLine1",
            "baseName": "address_line1",
            "type": "string"
        },
        {
            "name": "addressLine2",
            "baseName": "address_line2",
            "type": "string"
        },
        {
            "name": "addressLine3",
            "baseName": "address_line3",
            "type": "string"
        },
        {
            "name": "addressLine4",
            "baseName": "address_line4",
            "type": "string"
        },
        {
            "name": "city",
            "baseName": "city",
            "type": "string"
        },
        {
            "name": "contactPerson",
            "baseName": "contact_person",
            "type": "string"
        },
        {
            "name": "distict",
            "baseName": "distict",
            "type": "string"
        },
        {
            "name": "firstName",
            "baseName": "first_name",
            "type": "string"
        },
        {
            "name": "firstNameLocalScript",
            "baseName": "first_name_local_script",
            "type": "string"
        },
        {
            "name": "fullAddress",
            "baseName": "full_address",
            "type": "string"
        },
        {
            "name": "geolocation",
            "baseName": "geolocation",
            "type": "Logistics202309GetWarehouseListResponseDataWarehousesAddressGeolocation"
        },
        {
            "name": "lastName",
            "baseName": "last_name",
            "type": "string"
        },
        {
            "name": "lastNameLocalScript",
            "baseName": "last_name_local_script",
            "type": "string"
        },
        {
            "name": "phoneNumber",
            "baseName": "phone_number",
            "type": "string"
        },
        {
            "name": "postalCode",
            "baseName": "postal_code",
            "type": "string"
        },
        {
            "name": "region",
            "baseName": "region",
            "type": "string"
        },
        {
            "name": "regionCode",
            "baseName": "region_code",
            "type": "string"
        },
        {
            "name": "state",
            "baseName": "state",
            "type": "string"
        },
        {
            "name": "town",
            "baseName": "town",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseListResponseDataWarehousesAddress.attributeTypeMap;
    }
}

