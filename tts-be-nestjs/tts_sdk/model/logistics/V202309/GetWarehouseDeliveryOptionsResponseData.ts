/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptions } from './GetWarehouseDeliveryOptionsResponseDataDeliveryOptions';

export class Logistics202309GetWarehouseDeliveryOptionsResponseData {
    /**
    * List of deliver options available through the seller\'s warehouse, and the respective carriers and attribute restrictions depending on the commodity.
    */
    'deliveryOptions'?: Array<Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptions>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "deliveryOptions",
            "baseName": "delivery_options",
            "type": "Array<Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptions>"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseDeliveryOptionsResponseData.attributeTypeMap;
    }
}

