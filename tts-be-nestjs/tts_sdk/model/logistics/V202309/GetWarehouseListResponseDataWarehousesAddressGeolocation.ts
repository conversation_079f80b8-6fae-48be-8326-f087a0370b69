/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Logistics202309GetWarehouseListResponseDataWarehousesAddressGeolocation {
    /**
    * The latitude of the address.
    */
    'latitude'?: string;
    /**
    * The longitude of the address.
    */
    'longitude'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "latitude",
            "baseName": "latitude",
            "type": "string"
        },
        {
            "name": "longitude",
            "baseName": "longitude",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseListResponseDataWarehousesAddressGeolocation.attributeTypeMap;
    }
}

