/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetGlobalSellerWarehouseResponseDataGlobalWarehouses } from './GetGlobalSellerWarehouseResponseDataGlobalWarehouses';

export class Logistics202309GetGlobalSellerWarehouseResponseData {
    /**
    * Global warehouse information.
    */
    'globalWarehouses'?: Array<Logistics202309GetGlobalSellerWarehouseResponseDataGlobalWarehouses>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "globalWarehouses",
            "baseName": "global_warehouses",
            "type": "Array<Logistics202309GetGlobalSellerWarehouseResponseDataGlobalWarehouses>"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetGlobalSellerWarehouseResponseData.attributeTypeMap;
    }
}

