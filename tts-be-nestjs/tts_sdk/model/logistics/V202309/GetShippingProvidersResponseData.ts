/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetShippingProvidersResponseDataShippingProviders } from './GetShippingProvidersResponseDataShippingProviders';

export class Logistics202309GetShippingProvidersResponseData {
    /**
    * shipping provider list
    */
    'shippingProviders'?: Array<Logistics202309GetShippingProvidersResponseDataShippingProviders>;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "shippingProviders",
            "baseName": "shipping_providers",
            "type": "Array<Logistics202309GetShippingProvidersResponseDataShippingProviders>"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetShippingProvidersResponseData.attributeTypeMap;
    }
}

