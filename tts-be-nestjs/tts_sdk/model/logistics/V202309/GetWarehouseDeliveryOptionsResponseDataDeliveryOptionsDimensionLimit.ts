/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit {
    /**
    * Maximum height limit.
    */
    'maxHeight'?: number;
    /**
    * Maximum length limit.
    */
    'maxLength'?: number;
    /**
    * Maximum width limit.
    */
    'maxWidth'?: number;
    /**
    * The unit of measurement for the dimensions, with possible values: - CM - INCH
    */
    'unit'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "maxHeight",
            "baseName": "max_height",
            "type": "number"
        },
        {
            "name": "maxLength",
            "baseName": "max_length",
            "type": "number"
        },
        {
            "name": "maxWidth",
            "baseName": "max_width",
            "type": "number"
        },
        {
            "name": "unit",
            "baseName": "unit",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit.attributeTypeMap;
    }
}

