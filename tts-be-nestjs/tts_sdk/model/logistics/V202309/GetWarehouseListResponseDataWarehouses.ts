/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetWarehouseListResponseDataWarehousesAddress } from './GetWarehouseListResponseDataWarehousesAddress';

export class Logistics202309GetWarehouseListResponseDataWarehouses {
    'address'?: Logistics202309GetWarehouseListResponseDataWarehousesAddress;
    /**
    * Possible values: - ENABLED: All products in stock are available for sale. - DISABLED: All products in stock are unavailable for sale.  - RESTRICTED: The warehouse is either on \"holiday mode\" or \"order limit mode.\" All products in stock are unavailable for sale. -Holiday mode: When the seller cannot fulfill an order from a warehouse, the seller can turn on holiday mode for the warehouse in seller center.  - Order limit mode: When the seller violates TikTok Shop policies, TikTok Shop will limit the order volume that can be fulfilled by a warehouse.
    */
    'effectStatus'?: string;
    /**
    * The warehouse ID, a unique and immutable primary key, used for all warehouse logistics.
    */
    'id'?: string;
    /**
    * The default warehouse.  If a product is listed with no designated warehouse, the default warehouse will be used.
    */
    'isDefault'?: boolean;
    /**
    * Warehouse name. This name is not unique across the TikTok Shop system.
    */
    'name'?: string;
    /**
    * Possible values: - DOMESTIC_WAREHOUSE: The warehouse is in the same country as the target market and the seller. - CB_OVERSEA_WAREHOUSE: For cross-border sellers, a local warehouse in the target market.  - CB_DIRECT_SHIPPING_WAREHOUSE: For cross-border sellers, a warehouse in the seller\'s base country, e.g., Mainland China or Hong Kong.
    */
    'subType'?: string;
    /**
    * Possible values: - SALES_WAREHOUSE: Warehouse for shipping products.   - RETURN_WAREHOUSE: Warehouse for receiving returned products.  You can have the same warehouse for both shipping and receiving returns, but they will have different warehouse IDs with the same address.
    */
    'type'?: string;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "address",
            "baseName": "address",
            "type": "Logistics202309GetWarehouseListResponseDataWarehousesAddress"
        },
        {
            "name": "effectStatus",
            "baseName": "effect_status",
            "type": "string"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "isDefault",
            "baseName": "is_default",
            "type": "boolean"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "subType",
            "baseName": "sub_type",
            "type": "string"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseListResponseDataWarehouses.attributeTypeMap;
    }
}

