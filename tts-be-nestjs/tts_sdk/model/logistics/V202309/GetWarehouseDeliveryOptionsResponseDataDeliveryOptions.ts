/**
 * tiktok shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';
import { Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit } from './GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit';
import { Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsWeightLimit } from './GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsWeightLimit';

export class Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptions {
    /**
    * Delivery option description.
    */
    'description'?: string;
    'dimensionLimit'?: Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit;
    /**
    * Delivery option ID.
    */
    'id'?: string;
    /**
    * Delivery option name.
    */
    'name'?: string;
    /**
    * The platform on which the delivery option is available Possible values: - TIKTOK_SHOP - TOKOPEDIA
    */
    'platform'?: Array<string>;
    /**
    * Delivery option type. This is an enumerated type with values: - STANDARD - EXPRESS - ECONOMY - SEND_BY_SELLER
    */
    'type'?: string;
    'weightLimit'?: Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsWeightLimit;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "description",
            "baseName": "description",
            "type": "string"
        },
        {
            "name": "dimensionLimit",
            "baseName": "dimension_limit",
            "type": "Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsDimensionLimit"
        },
        {
            "name": "id",
            "baseName": "id",
            "type": "string"
        },
        {
            "name": "name",
            "baseName": "name",
            "type": "string"
        },
        {
            "name": "platform",
            "baseName": "platform",
            "type": "Array<string>"
        },
        {
            "name": "type",
            "baseName": "type",
            "type": "string"
        },
        {
            "name": "weightLimit",
            "baseName": "weight_limit",
            "type": "Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptionsWeightLimit"
        }    ];

    static getAttributeTypeMap() {
        return Logistics202309GetWarehouseDeliveryOptionsResponseDataDeliveryOptions.attributeTypeMap;
    }
}

