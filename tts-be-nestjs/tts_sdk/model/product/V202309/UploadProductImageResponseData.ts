/**
 * tik<PERSON> shop openapi
 * sdk for apis
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { RequestFile } from '../../models';

export class Product202309UploadProductImageResponseData {
    /**
    * The height of the image after aspect ratio adjustment. 
    */
    'height'?: number;
    /**
    * The URI to identify the image in API requests and responses. Pass this value when creating or editing a product to associate the image with the product.
    */
    'uri'?: string;
    /**
    * The URL to access and view the image. Use this URL in product descriptions by embedding it within an HTML `<img>` tag.
    */
    'url'?: string;
    /**
    * The usage scenario specified during upload. Possible values: - MAIN_IMAGE - ATTRIBUTE_IMAGE - DESCRIPTION_IMAGE - CERTIFICATION_IMAGE - SIZE_CHART_IMAGE
    */
    'useCase'?: string;
    /**
    * The width of the image after aspect ratio adjustment. 
    */
    'width'?: number;

    static discriminator: string | undefined = undefined;

    static attributeTypeMap: Array<{name: string, baseName: string, type: string}> = [
        {
            "name": "height",
            "baseName": "height",
            "type": "number"
        },
        {
            "name": "uri",
            "baseName": "uri",
            "type": "string"
        },
        {
            "name": "url",
            "baseName": "url",
            "type": "string"
        },
        {
            "name": "useCase",
            "baseName": "use_case",
            "type": "string"
        },
        {
            "name": "width",
            "baseName": "width",
            "type": "number"
        }    ];

    static getAttributeTypeMap() {
        return Product202309UploadProductImageResponseData.attributeTypeMap;
    }
}

