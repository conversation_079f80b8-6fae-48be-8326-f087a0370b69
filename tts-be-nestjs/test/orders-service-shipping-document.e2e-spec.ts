import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { OrdersService } from '../src/orders/orders.service';
import { OrdersModule } from '../src/orders/orders.module';
import { TikTokShopModule } from '../src/tiktok-shop/tiktok-shop.module';
import { TikTokPackage } from '../src/orders/entities/tiktok-package.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';

describe('OrdersService - Shipping Document Integration (e2e)', () => {
  let app: INestApplication;
  let ordersService: OrdersService;
  let tikTokPackageRepository: Repository<TikTokPackage>;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
        }),
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => ({
            type: 'postgres',
            host: configService.get('DB_HOST'),
            port: configService.get<number>('DB_PORT'),
            username: configService.get('DB_USERNAME'),
            password: configService.get('DB_PASSWORD'),
            database: configService.get('DB_DATABASE'),
            entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
            synchronize: false,
            ssl: configService.get('NODE_ENV') === 'production' ? { rejectUnauthorized: false } : false,
          }),
        }),
        OrdersModule,
        TikTokShopModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    ordersService = moduleFixture.get<OrdersService>(OrdersService);
    tikTokPackageRepository = moduleFixture.get<Repository<TikTokPackage>>(
      getRepositoryToken(TikTokPackage),
    );
  });

  afterAll(async () => {
    await app.close();
  });

  describe('TikTokPackage Entity', () => {
    it('should have rawTikTokResponseDocument column', async () => {
      // Test that the new column exists by creating a package with the new field
      const testPackage = tikTokPackageRepository.create({
        packageIdTT: 'test-package-id',
        orderIdTT: 'test-order-id',
        orderId: 1,
        userId: 1,
        rawTikTokResponse: { test: 'data' },
        rawTikTokResponseDocument: { 
          code: 0,
          data: {
            docUrl: 'https://example.com/shipping-label.pdf',
            trackingNumber: 'TEST123456789'
          },
          message: 'success',
          requestId: 'test-request-id'
        },
      });

      // This should not throw an error if the column exists
      expect(testPackage.rawTikTokResponseDocument).toBeDefined();
      expect(testPackage.rawTikTokResponseDocument.data.docUrl).toBe('https://example.com/shipping-label.pdf');
      expect(testPackage.rawTikTokResponseDocument.data.trackingNumber).toBe('TEST123456789');
    });
  });

  describe('getPackageShippingDocument method', () => {
    it('should be defined as a private method', () => {
      // Check that the method exists (even though it's private, we can check the service has it)
      expect(ordersService).toBeDefined();
      
      // We can't directly test the private method, but we can verify the service compiles
      // and the method signature is correct by checking the service instance
      const servicePrototype = Object.getPrototypeOf(ordersService);
      const methodNames = Object.getOwnPropertyNames(servicePrototype);
      
      expect(methodNames).toContain('getPackageShippingDocument');
    });
  });

  describe('createPackage integration', () => {
    it('should have the shipping document workflow integrated', () => {
      // This test verifies that the createPackage method exists and can be called
      // In a real test, you would mock the TikTok API calls and test the full workflow
      expect(ordersService.createPackage).toBeDefined();
      expect(typeof ordersService.createPackage).toBe('function');
    });
  });
});
