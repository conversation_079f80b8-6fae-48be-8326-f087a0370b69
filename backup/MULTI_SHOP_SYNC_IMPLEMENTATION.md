# Multi-Shop Order Synchronization Implementation

## Overview

This document describes the implementation of the new `synchronizeOrdersMultiShop` endpoint that allows synchronizing orders from multiple TikTok shops simultaneously.

## Implementation Details

### 1. New DTO (Data Transfer Object)

**File**: `tts-be-nestjs/src/orders/dto/order-sync.dto.ts`

Added `OrderSyncMultiShopDto` class with the following properties:
- `tiktokShopIds: number[]` - Array of TikTok Shop IDs to synchronize
- All other properties from the original `OrderSyncDto` (pageSize, sortOrder, etc.)
- Proper validation decorators for input validation

### 2. Enhanced Queue Service

**File**: `tts-be-nestjs/src/queues/services/order-queue.service.ts`

Added `addOrderSyncMultiShopJobs` method that:
- Accepts an array of TikTok Shop IDs
- Validates that all shops exist and belong to the user
- Fetches shop information including friendly names
- Creates individual jobs for each shop
- Returns an array of job results with enhanced metadata

**Key Features**:
- **Shop Validation**: Verifies all shops exist and belong to the requesting user
- **Friendly Names**: Includes shop friendly names in job data for better identification
- **Individual Jobs**: Creates separate jobs for each shop to enable parallel processing
- **Enhanced Metadata**: Each job result includes shop ID, name, and job ID
- **Error Handling**: Proper error handling for missing or unauthorized shops

### 3. New Controller Endpoint

**File**: `tts-be-nestjs/src/orders/orders.controller.ts`

Added `synchronizeOrdersMultiShop` endpoint:
- **Route**: `POST /orders/synchronize-multi-shop`
- **Input**: `OrderSyncMultiShopDto`
- **Output**: Array of job results with shop information
- **Validation**: Full DTO validation with proper error responses
- **Documentation**: Complete Swagger/OpenAPI documentation

### 4. Frontend Types

**File**: `tts-fe-nextjs/src/types/order.ts`

Added TypeScript interfaces:
- `OrderSyncMultiShopDto` - Input interface for the new endpoint
- `OrderSyncMultiShopJobResult` - Response interface for job results

## API Usage

### Request Example

```typescript
POST /orders/synchronize-multi-shop
Content-Type: application/json
Authorization: Bearer <token>

{
  "tiktokShopIds": [1, 2, 3],
  "pageSize": 50,
  "sortOrder": "DESC",
  "sortField": "CREATE_TIME",
  "maxPages": 10,
  "forceFullSync": false
}
```

### Response Example

```typescript
[
  {
    "jobId": "12345",
    "tiktokShopId": 1,
    "shopName": "My Beauty Store",
    "status": "queued"
  },
  {
    "jobId": "12346",
    "tiktokShopId": 2,
    "shopName": "Electronics Shop",
    "status": "queued"
  },
  {
    "jobId": "12347",
    "tiktokShopId": 3,
    "shopName": "Fashion Boutique",
    "status": "queued"
  }
]
```

## Job Processing

### Individual Job Data

Each job created by the multi-shop endpoint includes:
```typescript
{
  syncDto: OrderSyncDto,        // Individual shop sync parameters
  userId: number,               // User ID for authorization
  shopFriendlyName: string      // Shop friendly name for identification
}
```

### Job Monitoring

- Each job can be monitored individually using the existing `/orders/sync-job-status/:jobId` endpoint
- Jobs are processed by the existing `OrderQueueProcessor`
- No changes needed to the job processing logic

## Error Handling

### Validation Errors (400)
- Invalid input parameters
- Empty shop IDs array
- Invalid data types

### Authorization Errors (404)
- Shop not found
- Shop doesn't belong to the user
- Multiple shops with access issues

### Example Error Response
```typescript
{
  "statusCode": 404,
  "message": "TikTok Shops with IDs [5, 6] not found or access denied"
}
```

## Benefits

1. **Parallel Processing**: Each shop is processed in a separate job, enabling parallel execution
2. **Better Monitoring**: Individual job IDs allow granular progress tracking
3. **Enhanced Metadata**: Shop names included for better user experience
4. **Reusable Logic**: Leverages existing job processing infrastructure
5. **Scalable**: Can handle any number of shops (limited by validation)

## Testing

A test script has been created at `tts-fe-nextjs/test-multi-shop-sync.js` that:
- Tests the new endpoint with valid data
- Tests input validation with invalid data
- Demonstrates job status checking
- Provides examples for integration

## Migration Notes

- **Backward Compatibility**: The original `synchronizeOrders` endpoint remains unchanged
- **No Database Changes**: Uses existing entities and relationships
- **Queue Infrastructure**: Leverages existing Bull queue setup
- **Dependencies**: No new dependencies required

## Future Enhancements

Potential improvements that could be added:
1. **Batch Status Endpoint**: Get status of multiple jobs at once
2. **Progress Aggregation**: Combined progress across all shops
3. **Priority Queuing**: Different priorities for different shops
4. **Rate Limiting**: Per-shop rate limiting for API calls
