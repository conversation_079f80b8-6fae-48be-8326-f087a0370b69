// Auth related hooks
import { useNextAuth } from './use-next-auth';
import { useSession } from 'next-auth/react';

// Data fetching hooks (React Query)
import { useAttributes } from './use-attributes-query';
import { useBrands, useBrandById } from './use-brands-query';
import { useCategories, useCategoryById } from './use-categories-query';
import {
  useProducts,
  useProduct,
  useSynchronizeProducts,
  useSynchronizeProductsMultiShop,
  useExportProducts
} from './use-products-query';
import { useShopsForAdmin } from './use-shops-query';
import {
  useTikTokShops,
  useTikTokShop,
  useTikTokShopWarehouses,
  useUpdateTikTokShop,
  useRefreshTikTokShopToken,
  useSyncTikTokShopWarehouses
} from './use-tiktok-shops-query';
import { useUsers } from './use-users-query';
import { useWarehouses } from './use-warehouses-query';
import {
  useGearmentStockStatus,
  useSyncGearmentStockStatus,
  useGenerateSkusFromGearment
} from './use-gearment-query';
import {
  useStagedProducts,
  useStagedProduct,
  useStagedProductUploads,
  useCreateStagedProduct,
  useUpdateStagedProduct,
  useDeleteStagedProduct,
  useUploadToShop
} from './use-staged-products-query';
import {
  useTemplates,
  useTemplate,
  useCreateTemplate,
  useUpdateTemplate,
  useDeleteTemplate,
  useCreateStagedProductFromTemplate,
  useCreateStagedProductFromTemplateAndUpload
} from './use-templates-query';
import {
  useCrawledProducts,
  useCrawledProduct,
  useCreateCrawledProduct,
  useDeleteCrawledProduct,
  useBulkDeleteCrawledProducts
} from './use-crawled-products-query';
import {
  useOrders,
  useOrder,
  useOrderByTikTokId,
  useOrderStatistics,
  useOrderFulfillmentOptions,
  useSynchronizeOrders,
  useSynchronizeOrdersMultiShop,
  useSynchronizeOrderDetails,
  useUpdateOrderTracking,
  useExportOrders,
  useCreatePackage
} from './use-orders-query';

export {
  // Auth hooks
  useNextAuth,
  useSession,

  // Data fetching hooks
  useAttributes,
  useBrands,
  useBrandById,
  useCategories,
  useCategoryById,
  useProducts,
  useProduct,
  useSynchronizeProducts,
  useSynchronizeProductsMultiShop,
  useExportProducts,
  useShopsForAdmin,
  useTikTokShops,
  useTikTokShop,
  useTikTokShopWarehouses,
  useUpdateTikTokShop,
  useRefreshTikTokShopToken,
  useSyncTikTokShopWarehouses,
  useUsers,
  useWarehouses,
  useGearmentStockStatus,
  useSyncGearmentStockStatus,
  useGenerateSkusFromGearment,

  // Staged product hooks
  useStagedProducts,
  useStagedProduct,
  useStagedProductUploads,
  useCreateStagedProduct,
  useUpdateStagedProduct,
  useDeleteStagedProduct,
  useUploadToShop,

  // Template hooks
  useTemplates,
  useTemplate,
  useCreateTemplate,
  useUpdateTemplate,
  useDeleteTemplate,
  useCreateStagedProductFromTemplate,
  useCreateStagedProductFromTemplateAndUpload,

  // Crawled product hooks
  useCrawledProducts,
  useCrawledProduct,
  useCreateCrawledProduct,
  useDeleteCrawledProduct,
  useBulkDeleteCrawledProducts,

  // Order hooks
  useOrders,
  useOrder,
  useOrderByTikTokId,
  useOrderStatistics,
  useOrderFulfillmentOptions,
  useSynchronizeOrders,
  useSynchronizeOrdersMultiShop,
  useSynchronizeOrderDetails,
  useUpdateOrderTracking,
  useExportOrders,
  useCreatePackage,
};
