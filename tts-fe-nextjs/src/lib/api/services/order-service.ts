'use client';

import { apiRequest } from '@/lib/api/api-client';
import { PaginatedResult } from '@/types/pagination';
import {
  Order,
  OrderQueryDto,
  OrderSyncDto,
  OrderSyncJobResult,
  OrderSyncMultiShopDto,
  OrderSyncMultiShopJobResult,
  OrderSyncResult,
  CreatePackageDto,
  CreatePackageResponseDto
} from '@/types/order';

/**
 * Fetch all orders with pagination and filtering
 */
export const getOrders = async (
  params?: OrderQueryDto,
  token?: string
): Promise<PaginatedResult<Order>> => {
  return apiRequest<PaginatedResult<Order>>({
    url: '/orders',
    method: 'GET',
    params,
    token
  });
};

/**
 * Fetch a single order by ID
 */
export const getOrder = async (
  id: number,
  token?: string
): Promise<Order> => {
  return apiRequest<Order>({
    url: `/orders/${id}`,
    method: 'GET',
    token
  });
};

/**
 * Fetch a single order by TikTok order ID
 */
export const getOrderByTikTokId = async (
  idTT: string,
  token?: string
): Promise<Order> => {
  return apiRequest<Order>({
    url: `/orders/tiktok/${idTT}`,
    method: 'GET',
    token
  });
};

/**
 * Synchronize orders from TikTok Shop
 */
export const synchronizeOrders = async (
  data: OrderSyncDto,
  token?: string
): Promise<OrderSyncJobResult> => {
  return apiRequest<OrderSyncJobResult>({
    url: '/orders/synchronize',
    method: 'POST',
    data,
    token
  });
};

/**
 * Synchronize orders from multiple TikTok Shops
 */
export const synchronizeOrdersMultiShop = async (
  data: OrderSyncMultiShopDto,
  token?: string
): Promise<OrderSyncMultiShopJobResult[]> => {
  return apiRequest<OrderSyncMultiShopJobResult[]>({
    url: '/orders/synchronize-multi-shop',
    method: 'POST',
    data,
    token
  });
};

/**
 * Synchronize order details from TikTok Shop
 */
export const synchronizeOrderDetails = async (
  data: {
    tiktokShopId: number;
    orderIds: string[];
  },
  token?: string
): Promise<OrderSyncResult> => {
  return apiRequest<OrderSyncResult>({
    url: '/orders/synchronize-details',
    method: 'POST',
    data,
    token
  });
};

/**
 * Get order statistics for a TikTok shop
 */
export const getOrderStatistics = async (
  tiktokShopId: number,
  token?: string
): Promise<{
  totalOrders: number;
  ordersByStatus: Record<string, number>;
  totalRevenue: number;
  averageOrderValue: number;
}> => {
  return apiRequest({
    url: `/orders/statistics/${tiktokShopId}`,
    method: 'GET',
    token
  });
};

/**
 * Export orders to CSV
 */
export const exportOrders = async (
  params?: OrderQueryDto,
  token?: string
): Promise<Blob> => {
  return apiRequest<Blob>({
    url: '/orders/export',
    method: 'GET',
    params,
    token,
    responseType: 'blob'
  });
};

/**
 * Update order tracking information
 */
export const updateOrderTracking = async (
  id: number,
  data: {
    trackingNumber?: string;
    shippingProvider?: string;
    shippingProviderId?: string;
  },
  token?: string
): Promise<Order> => {
  return apiRequest<Order>({
    url: `/orders/${id}/tracking`,
    method: 'PATCH',
    data,
    token
  });
};

/**
 * Get order fulfillment options for a TikTok shop
 */
export const getOrderFulfillmentOptions = async (
  tiktokShopId: number,
  token?: string
): Promise<{
  fulfillmentTypes: string[];
  shippingProviders: string[];
  deliveryOptions: Array<{
    id: string;
    name: string;
  }>;
}> => {
  return apiRequest({
    url: `/orders/fulfillment-options/${tiktokShopId}`,
    method: 'GET',
    token
  });
};

/**
 * Create a package for an order
 */
export const createPackage = async (
  data: CreatePackageDto,
  token?: string
): Promise<CreatePackageResponseDto> => {
  return apiRequest<CreatePackageResponseDto>({
    url: '/orders/create-package',
    method: 'POST',
    data,
    token
  });
};
