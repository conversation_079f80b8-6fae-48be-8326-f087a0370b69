'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useNextAuth } from '@/lib/hooks';
import { ImagePreview } from '@/components/ui/image-preview';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Edit,
  Eye,
  Trash,
  Copy,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Template } from '@/types/template';
import { formatDate } from '@/lib/utils';

interface TemplateTableProps {
  templates: Template[];
  isLoading: boolean;
  onDelete: (id: number) => void;
  onSelectAll: (selected: boolean) => void;
  onSelectTemplate: (id: number, selected: boolean) => void;
  selectedTemplateIds: number[];
}

export function TemplateTable({
  templates,
  isLoading,
  onDelete,
  onSelectAll,
  onSelectTemplate,
  selectedTemplateIds,
}: TemplateTableProps) {
  const router = useRouter();
  const { user } = useNextAuth();

  // Helper function to check if current user owns the template
  const isOwner = (template: Template) => user?.id === template.userId;

  // Check if all templates are selected
  const allSelected =
    templates.length > 0 && selectedTemplateIds.length === templates.length;

  // Check if some templates are selected
  const someSelected =
    selectedTemplateIds.length > 0 && selectedTemplateIds.length < templates.length;

  // Handle view template
  const handleView = (id: number) => {
    router.push(`/client/template/${id}`);
  };

  // Handle edit template
  const handleEdit = (id: number) => {
    router.push(`/client/template/${id}/edit`);
  };

  // Handle row click to navigate to detail page
  const handleRowClick = (templateId: number) => {
    router.push(`/client/template/${templateId}`);
  };

  // Render loading skeleton
  if (isLoading) {
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12"></TableHead>
              <TableHead>Size Chart</TableHead>
              <TableHead>Template Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Public</TableHead>
              <TableHead>Attributes</TableHead>
              <TableHead>SKUs</TableHead>
              <TableHead>Updated At</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                <TableCell><Skeleton className="h-12 w-12" /></TableCell>
                <TableCell><Skeleton className="h-4 w-40" /></TableCell>
                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                <TableCell><Skeleton className="h-8 w-24 ml-auto" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}
                indeterminate={someSelected}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead>Size Chart</TableHead>
            <TableHead className="max-w-xs">Template Name</TableHead>
            <TableHead>Category</TableHead>
            <TableHead>Public</TableHead>
            <TableHead>Attributes</TableHead>
            <TableHead>SKUs</TableHead>
            <TableHead>Updated At</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {templates.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                No templates found. Create your first template to get started.
              </TableCell>
            </TableRow>
          ) : (
            templates.map((template) => (
              <TableRow
                key={template.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleRowClick(template.id)}
              >
                <TableCell onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedTemplateIds.includes(template.id)}
                    onCheckedChange={(checked) =>
                      onSelectTemplate(template.id, !!checked)
                    }
                  />
                </TableCell>
                <TableCell onClick={(e) => e.stopPropagation()}>
                  {template.sizeChart?.imageUrl ? (
                    <ImagePreview
                      src={template.sizeChart.imageUrl}
                      alt={`${template.name} size chart`}
                      productName={`${template.name} - Size Chart`}
                      thumbnailSize="h-12 w-12"
                    />
                  ) : (
                    <div className="h-12 w-12 rounded-md bg-muted flex items-center justify-center text-xs text-muted-foreground">
                      No image
                    </div>
                  )}
                </TableCell>
                <TableCell className="max-w-xs">
                  <div className="min-w-0 flex-1">
                    <div className="font-medium truncate" title={template.name}>
                      {template.name}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-[150px]">
                    <div className="truncate font-medium" title={template.category?.localName || 'N/A'}>
                      {template.category?.localName || 'N/A'}
                    </div>
                    <div className="text-xs text-muted-foreground truncate" title={template.brand?.name || 'N/A'}>
                      {template.brand?.name || 'N/A'}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={template.isPublic ? "default" : "outline"}>
                    {template.isPublic ? 'Yes' : 'No'}
                  </Badge>
                </TableCell>
                <TableCell>{template.productAttributes?.length || 0}</TableCell>
                <TableCell>{template.skus?.length || 0}</TableCell>
                <TableCell>{formatDate(template.updatedAt)}</TableCell>
                <TableCell className="text-right" onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleView(template.id)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View
                      </DropdownMenuItem>
                      {isOwner(template) && (
                        <>
                          <DropdownMenuItem onClick={() => handleEdit(template.id)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onDelete(template.id)}
                            className="text-destructive focus:text-destructive"
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
