'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ImagePreview } from '@/components/ui/image-preview';
import { MoreHorizontal, Upload, Edit, Trash, Eye, History } from 'lucide-react';
import { StagedProductWithUpload, ProductUploadStatus } from '@/types/staged-product';
import { formatDate } from '@/lib/utils';

interface StagedProductsTableProps {
  products: StagedProductWithUpload[];
  isLoading: boolean;
  onUpload: (productIds: number[]) => void;
  onDelete: (productId: number) => void;
  onSelectAll: (selected: boolean) => void;
  onSelectProduct: (productId: number, selected: boolean) => void;
  selectedProductIds: number[];
  isUploading?: boolean;
  isDeleting?: boolean;
}

export function StagedProductsTable({
  products,
  isLoading,
  onUpload,
  onDelete,
  onSelectAll,
  onSelectProduct,
  selectedProductIds,
  isUploading = false,
  isDeleting = false,
}: StagedProductsTableProps) {
  const router = useRouter();

  // Helper function to get image URL from ProductImageInput
  const getImageUrl = (image: any): string | null => {
    if (typeof image === 'string') {
      return image; // Handle legacy string format if any
    }
    return image?.imageUrl || image?.r2Key || null;
  };

  // Function to get status badge based on upload status
  const getStatusBadge = (status: ProductUploadStatus) => {
    switch (status) {
      case ProductUploadStatus.PENDING:
        return <Badge variant="outline">Pending</Badge>;
      case ProductUploadStatus.IN_PROGRESS:
        return <Badge variant="secondary">In Progress</Badge>;
      case ProductUploadStatus.COMPLETED:
        return <Badge variant="default" className="bg-green-500 hover:bg-green-600">Completed</Badge>;
      case ProductUploadStatus.FAILED:
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  // Check if all products are selected
  const allSelected = products.length > 0 && selectedProductIds.length === products.length;

  // Check if some products are selected
  const someSelected = selectedProductIds.length > 0 && selectedProductIds.length < products.length;

  // Handle row click to navigate to detail page
  const handleRowClick = (productId: number) => {
    router.push(`/client/tiktok/upload/${productId}`);
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow key="header-row">
            <TableHead key="header-checkbox" className="w-12">
              <Checkbox
                checked={allSelected}
                indeterminate={someSelected}
                onCheckedChange={onSelectAll}
                aria-label="Select all products"
              />
            </TableHead>
            <TableHead key="header-image" className="w-20">Image</TableHead>
            <TableHead key="header-title" className="max-w-xs">Title</TableHead>
            <TableHead key="header-skus" className="w-16">SKUs</TableHead>
            <TableHead key="header-updated" className="w-32">Updated At</TableHead>
            <TableHead key="header-upload" className="w-40">Latest Upload</TableHead>
            <TableHead key="header-actions" className="w-16">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {(() => {
            if (isLoading) {
              return Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`loading-${index}`}>
                  <TableCell><Skeleton className="h-4 w-4" /></TableCell>
                  <TableCell><Skeleton className="h-12 w-12 rounded-md" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-8" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-8 ml-auto" /></TableCell>
                </TableRow>
              ));
            }

            if (products.length === 0) {
              return (
                <TableRow key="empty">
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No staged products found. Create your first product to get started.
                  </TableCell>
                </TableRow>
              );
            }

            return products.map((productWithUpload, index) => {
              const { product, latestUpload } = productWithUpload;
              return (
              <TableRow
                key={`product-${product.id}-${index}`}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleRowClick(product.id)}
              >
                <TableCell key={`checkbox-${product.id}`} onClick={(e) => e.stopPropagation()}>
                  <Checkbox
                    checked={selectedProductIds.includes(product.id)}
                    onCheckedChange={(checked) => onSelectProduct(product.id, !!checked)}
                    aria-label={`Select product ${product.title}`}
                  />
                </TableCell>
                <TableCell key={`image-${product.id}`} onClick={(e) => e.stopPropagation()}>
                  {(() => {
                    const firstImageUrl = product.images && product.images.length > 0
                      ? getImageUrl(product.images[0])
                      : null;

                    return firstImageUrl ? (
                      <ImagePreview
                        src={firstImageUrl}
                        alt={product.title}
                        productName={product.title}
                        thumbnailSize="h-12 w-12"
                      />
                    ) : (
                      <div className="h-12 w-12 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
                        No image
                      </div>
                    );
                  })()}
                </TableCell>
                <TableCell key={`title-${product.id}`} className="font-medium max-w-xs">
                  <div className="truncate" title={product.title}>
                    {product.title}
                  </div>
                </TableCell>
                <TableCell key={`skus-${product.id}`}>{product.skus?.length || 0}</TableCell>
                <TableCell key={`updated-${product.id}`}>{formatDate(product.updatedAt)}</TableCell>
                <TableCell key={`upload-${product.id}`}>
                  {latestUpload ? (
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-1">
                        {getStatusBadge(latestUpload.status)}
                      </div>
                      {latestUpload.shopFriendlyName && (
                        <div className="text-xs text-muted-foreground truncate" title={latestUpload.shopFriendlyName}>
                          {latestUpload.shopFriendlyName}
                        </div>
                      )}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Not uploaded</span>
                  )}
                </TableCell>
                <TableCell key={`actions-${product.id}`} onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel key={`label-${product.id}`}>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator key={`sep1-${product.id}`} />
                      <DropdownMenuItem key={`view-${product.id}`} onClick={() => router.push(`/client/tiktok/upload/${product.id}`)}>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem key={`edit-${product.id}`} onClick={() => router.push(`/client/tiktok/upload/${product.id}/edit`)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        key={`upload-${product.id}`}
                        onClick={() => onUpload([product.id])}
                        disabled={isUploading}
                      >
                        <Upload className="mr-2 h-4 w-4" />
                        Upload to Shop
                      </DropdownMenuItem>
                      <DropdownMenuSeparator key={`sep2-${product.id}`} />
                      <DropdownMenuItem
                        key={`delete-${product.id}`}
                        className="text-destructive focus:text-destructive"
                        onClick={() => onDelete(product.id)}
                        disabled={isDeleting}
                      >
                        <Trash className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              );
            });
          })()}
        </TableBody>
      </Table>
    </div>
  );
}
